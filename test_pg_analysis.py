#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PostgreSQL分析模块测试脚本
测试已移植的函数是否工作正常
"""

import asyncio
import sys
import traceback
from datetime import datetime

# 导入要测试的函数
from utils.database.db_pg_analysis import (
    convert_records_to_dataframe,
    convert_monthly_analysis_to_dataframe,
    convert_customer_analysis_to_dataframe,
    apply_basic_data_cleaning,
    apply_special_case_processing,
    get_business_type_mapping,
    calculate_aggregated_data,
    calculate_job_aggregated_data,
    get_booking_details_from_tokens_table
)

def test_data_conversion_functions():
    """测试数据转换函数"""
    print("=" * 50)
    print("测试数据转换函数")
    print("=" * 50)
    
    # 测试 convert_records_to_dataframe
    print("\n1. 测试 convert_records_to_dataframe")
    test_records = [
        {"customer_name": "客户A", "profit": 1000, "income": 5000},
        {"customer_name": "客户B", "profit": 2000, "income": 8000},
    ]
    columns_info = {"customer_name": "客户名称", "profit": "利润", "income": "收入"}
    
    df = convert_records_to_dataframe(test_records, columns_info)
    print(f"  - DataFrame shape: {df.shape}")
    print(f"  - DataFrame columns: {list(df.columns)}")
    print(f"  - Sample data:\n{df.head()}")
    
    # 测试 convert_monthly_analysis_to_dataframe  
    print("\n2. 测试 convert_monthly_analysis_to_dataframe")
    test_monthly_data = [
        {
            "year": 2024,
            "month": 1,
            "pro2_system_id": 86021,
            "sea_export_data": [{"profit": 1000, "income": 5000}],
            "air_data": [{"profit": 500, "income": 2000}]
        }
    ]
    
    df_monthly = convert_monthly_analysis_to_dataframe(test_monthly_data, "booking", columns_info)
    print(f"  - Monthly DataFrame shape: {df_monthly.shape}")
    print(f"  - Monthly DataFrame columns: {list(df_monthly.columns)}")
    
    print("✅ 数据转换函数测试通过")
    return True

def test_business_logic_functions():
    """测试业务逻辑函数"""
    print("\n" + "=" * 50)
    print("测试业务逻辑函数")
    print("=" * 50)
    
    # 测试 get_business_type_mapping
    print("\n1. 测试 get_business_type_mapping")
    mapping = get_business_type_mapping()
    print(f"  - 业务类型映射: {mapping}")
    expected_keys = ['海运出口', '海运进口', '海运三角贸易', '空运']
    for key in expected_keys:
        if key not in mapping:
            print(f"  ❌ 缺少业务类型: {key}")
            return False
    
    # 测试特例处理函数
    print("\n2. 测试 apply_basic_data_cleaning")
    test_record = {
        "pro2_system_id": 86021,
        "bill_pol": "",
        "job_pol": "SHA",
        "is_free_hand": 1,
    }
    
    cleaned_record = apply_basic_data_cleaning(test_record)
    print(f"  - 原始记录: {test_record}")
    print(f"  - 清理后记录: {cleaned_record}")
    
    # 测试86021特例处理
    print("\n3. 测试 apply_special_case_processing (86021特例)")
    test_86021_record = {
        "pro2_system_id": 86021,
        "is_free_hand": 1,
        "salesman_dept_name": "指定货业务",
        "salesman_name": "ABC指定货",
        "bill_pod": ""
    }
    
    processed_record = apply_special_case_processing(test_86021_record)
    print(f"  - 原始记录: {test_86021_record}")
    print(f"  - 处理后记录: {processed_record}")
    
    # 验证86021特例处理结果
    if processed_record.get('is_freehand') != 0:
        print("  ❌ 86021指定货业务处理错误: is_freehand应为0")
        return False
    
    if not processed_record.get('nomi_agent_name'):
        print("  ❌ 86021指定货业务处理错误: nomi_agent_name不能为空")
        return False
    
    if processed_record.get('bill_pod') != 'SHA':
        print("  ❌ 86021卸货港处理错误: 应设置为SHA")
        return False
    
    print("✅ 业务逻辑函数测试通过")
    return True

def test_calculation_functions():
    """测试计算汇总函数"""
    print("\n" + "=" * 50)
    print("测试计算汇总函数")
    print("=" * 50)
    
    # 测试 calculate_aggregated_data
    print("\n1. 测试 calculate_aggregated_data")
    test_records = [
        {
            "client_name": "客户A",
            "income": 5000,
            "cost": -3000,
            "profit": 2000,
            "lcl_rt": 10.5,
            "teu": 1,
            "is_freehand": 0,
            "nomi_agent_name": "代理A"
        },
        {
            "client_name": "客户B", 
            "income": 8000,
            "cost": -5000,
            "profit": 3000,
            "lcl_rt": 20.0,
            "teu": 2,
            "is_freehand": 1,
            "nomi_agent_name": ""
        }
    ]
    
    aggregated = calculate_aggregated_data(test_records)
    print(f"  - 汇总结果: {aggregated}")
    
    # 验证汇总结果
    expected_customer_count = 2
    expected_total_income = 13000
    expected_total_profit = 5000
    expected_nominated_count = 1
    expected_agent_count = 1
    
    if aggregated['customer_count'] != expected_customer_count:
        print(f"  ❌ 客户数量错误: 期望{expected_customer_count}, 实际{aggregated['customer_count']}")
        return False
        
    if abs(aggregated['income'] - expected_total_income) > 0.01:
        print(f"  ❌ 总收入错误: 期望{expected_total_income}, 实际{aggregated['income']}")
        return False
        
    if aggregated['all_nominated_count'] != expected_nominated_count:
        print(f"  ❌ 指定货数量错误: 期望{expected_nominated_count}, 实际{aggregated['all_nominated_count']}")
        return False
        
    if aggregated['nomi_agent_count'] != expected_agent_count:
        print(f"  ❌ 指定货代理数量错误: 期望{expected_agent_count}, 实际{aggregated['nomi_agent_count']}")
        return False
    
    print("✅ 计算汇总函数测试通过")
    return True

async def test_postgresql_connection():
    """测试PostgreSQL连接"""
    print("\n" + "=" * 50)
    print("测试PostgreSQL数据库连接")
    print("=" * 50)
    
    try:
        # 测试基本的PostgreSQL连接
        from utils.basic.data_conn_fb_pg import test_postgres_connection
        result = test_postgres_connection()
        if result:
            print("✅ PostgreSQL连接测试通过")
            return True
        else:
            print("❌ PostgreSQL连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ PostgreSQL连接测试异常: {e}")
        return False

async def test_core_query_function():
    """测试核心查询函数（使用小范围数据）"""
    print("\n" + "=" * 50)
    print("测试核心查询函数")
    print("=" * 50)
    
    try:
        # 测试查询函数的基本调用（使用很短的日期范围以减少数据量）
        print("\n测试 get_booking_details_from_tokens_table")
        
        # 使用近期的一个很短时间范围进行测试
        begin_date = "2024-12-01"
        end_date = "2024-12-01"  # 只查询一天的数据
        
        result = await get_booking_details_from_tokens_table(begin_date, end_date, 86021)
        
        print(f"  - 查询结果类型: {type(result)}")
        print(f"  - 包含的键: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
        
        if isinstance(result, dict):
            print(f"  - 数据条数: {result.get('total_count', 'Unknown')}")
            print(f"  - 查询信息: {result.get('query_info', {})}")
            
            if 'data' in result:
                data = result['data']
                print(f"  - 数据类型: {type(data)}")
                if isinstance(data, list) and len(data) > 0:
                    print(f"  - 第一条记录的键: {list(data[0].keys())}")
        
        print("✅ 核心查询函数基本结构正确")
        return True
        
    except Exception as e:
        print(f"❌ 核心查询函数测试失败: {e}")
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("开始PostgreSQL分析模块功能测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    test_results = []
    
    # 测试1: 数据转换函数
    try:
        result1 = test_data_conversion_functions()
        test_results.append(("数据转换函数", result1))
    except Exception as e:
        print(f"❌ 数据转换函数测试异常: {e}")
        test_results.append(("数据转换函数", False))
    
    # 测试2: 业务逻辑函数
    try:
        result2 = test_business_logic_functions() 
        test_results.append(("业务逻辑函数", result2))
    except Exception as e:
        print(f"❌ 业务逻辑函数测试异常: {e}")
        test_results.append(("业务逻辑函数", False))
    
    # 测试3: 计算汇总函数
    try:
        result3 = test_calculation_functions()
        test_results.append(("计算汇总函数", result3))
    except Exception as e:
        print(f"❌ 计算汇总函数测试异常: {e}")
        test_results.append(("计算汇总函数", False))
    
    # 测试4: PostgreSQL连接
    try:
        result4 = await test_postgresql_connection()
        test_results.append(("PostgreSQL连接", result4))
    except Exception as e:
        print(f"❌ PostgreSQL连接测试异常: {e}")
        test_results.append(("PostgreSQL连接", False))
    
    # 测试5: 核心查询函数
    try:
        result5 = await test_core_query_function()
        test_results.append(("核心查询函数", result5))
    except Exception as e:
        print(f"❌ 核心查询函数测试异常: {e}")
        test_results.append(("核心查询函数", False))
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print(f"\n总计: {len(test_results)}个测试, {passed}个通过, {failed}个失败")
    
    if failed == 0:
        print("🎉 所有测试通过！PostgreSQL分析模块基础架构正确。")
        return True
    else:
        print("⚠️  部分测试失败，需要修复问题。")
        return False

if __name__ == "__main__":
    asyncio.run(main())