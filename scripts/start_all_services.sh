#!/bin/bash

# 启动所有服务器上的 mcp-cms 服务

PASSWORD="2929!lxj#LXJ"
SERVICE_NAME="mcp-cms"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"

# 服务器配置（不使用关联数组，兼容老版本bash）
get_server() {
    case $1 in
        "qd") echo "<EMAIL>";;
        "sha") echo "<EMAIL>";;
        "tyo") echo "<EMAIL>";;
        "hkg") echo "<EMAIL>";;
    esac
}

print_info() {
    echo -e "\033[0;34mℹ️  $1\033[0m"
}

print_success() {
    echo -e "\033[0;32m✅ $1\033[0m"
}

print_error() {
    echo -e "\033[0;31m❌ $1\033[0m"
}

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

echo "🚀 启动所有服务器上的 mcp-cms 服务..."
echo ""

# 服务器列表
servers=("qd" "sha" "tyo" "hkg")

for server_key in "${servers[@]}"; do
    server=$(get_server "$server_key")
    ssh_cmd=$(get_ssh_cmd "$server_key")
    print_info "[$server_key] 启动服务: $server"

    # 启动服务
    if [[ "$server_key" == "hkg" ]]; then
        if $ssh_cmd "$server" "sudo systemctl start $SERVICE_NAME" 2>/dev/null; then
            # 检查服务状态
            sleep 2
            status=$($ssh_cmd "$server" "systemctl is-active $SERVICE_NAME" 2>/dev/null || echo "unknown")

            if [[ "$status" == "active" ]]; then
                print_success "[$server_key] 服务启动成功"
            else
                print_error "[$server_key] 服务启动失败，状态: $status"
            fi
        else
            print_error "[$server_key] 无法启动服务"
        fi
    else
        if $ssh_cmd "$server" "echo '$PASSWORD' | sudo -S systemctl start $SERVICE_NAME" 2>/dev/null; then
            # 检查服务状态
            sleep 2
            status=$($ssh_cmd "$server" "systemctl is-active $SERVICE_NAME" 2>/dev/null || echo "unknown")

            if [[ "$status" == "active" ]]; then
                print_success "[$server_key] 服务启动成功"
            else
                print_error "[$server_key] 服务启动失败，状态: $status"
            fi
        else
            print_error "[$server_key] 无法启动服务"
        fi
    fi
done

echo ""
echo "🔍 检查所有服务状态："
for server_key in "${servers[@]}"; do
    server=$(get_server "$server_key")
    status=$(sshpass -p "$PASSWORD" ssh -o StrictHostKeyChecking=no "$server" \
        "systemctl is-active $SERVICE_NAME" 2>/dev/null || echo "unknown")
    
    if [[ "$status" == "active" ]]; then
        print_success "[$server_key] $server - 运行中"
    else
        print_error "[$server_key] $server - 状态: $status"
    fi
done
