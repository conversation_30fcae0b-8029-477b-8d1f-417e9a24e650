#!/bin/bash

# 简化版停止所有服务器服务脚本

PASSWORD="2929!lxj#LXJ"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"

# 服务器配置
get_server() {
    case $1 in
        "qd") echo "<EMAIL>";;
        "sha") echo "<EMAIL>";;
        "tyo") echo "<EMAIL>";;
        "hkg") echo "<EMAIL>";;
    esac
}

get_service_name() {
    case $1 in
        "qd"|"sha"|"tyo"|"hkg") echo "mcp-cms";;
    esac
}

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

print_info() {
    echo -e "\033[0;34mℹ️  $1\033[0m"
}

print_success() {
    echo -e "\033[0;32m✅ $1\033[0m"
}

print_error() {
    echo -e "\033[0;31m❌ $1\033[0m"
}

echo "🛑 停止所有服务器上的 mcp-cms 服务..."
echo ""

servers=("qd" "sha" "tyo" "hkg")

for server_key in "${servers[@]}"; do
    server=$(get_server "$server_key")
    service_name=$(get_service_name "$server_key")
    ssh_cmd=$(get_ssh_cmd "$server_key")
    
    print_info "[$server_key] 停止服务: $server"
    
    if [[ "$server_key" == "hkg" ]]; then
        $ssh_cmd "$server" "sudo systemctl stop $service_name 2>/dev/null || true"
    else
        $ssh_cmd "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name 2>/dev/null || true"
    fi
    
    # 检查状态
    sleep 2
    status=$($ssh_cmd "$server" "systemctl is-active $service_name" 2>/dev/null || echo "inactive")
    
    if [[ "$status" == "inactive" ]]; then
        print_success "[$server_key] 服务停止成功"
    else
        print_error "[$server_key] 服务停止可能失败，状态: $status"
    fi
done

echo ""
print_success "所有服务器停止操作完成"
