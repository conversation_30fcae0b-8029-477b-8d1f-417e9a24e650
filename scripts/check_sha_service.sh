#!/bin/bash

PASSWORD="2929!lxj#LXJ"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"
server="<EMAIL>"

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

ssh_cmd=$(get_ssh_cmd "sha")

echo "🔍 检查上海服务器的详细状态..."
echo ""

echo "=== 服务状态 ==="
$ssh_cmd "$server" "systemctl status mcp-cms --no-pager -l"

echo ""
echo "=== 最近日志 ==="
$ssh_cmd "$server" "journalctl -u mcp-cms --no-pager -n 15"

echo ""
echo "=== 检查文件是否存在 ==="
$ssh_cmd "$server" "ls -la /home/<USER>/mcp-cms/mcp_server_cms.py"

echo ""
echo "=== 检查 uv 是否可用 ==="
$ssh_cmd "$server" "ls -la /home/<USER>/.local/bin/uv"
