#!/bin/bash

# 检查服务日志

PASSWORD="2929!lxj#LXJ"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

print_info() {
    echo -e "\033[0;34mℹ️  $1\033[0m"
}

# 检查第一个服务器的日志
server="<EMAIL>"
ssh_cmd=$(get_ssh_cmd "qd")
print_info "检查 QD 服务器的服务日志..."

echo "=== 最近的服务日志 ==="
$ssh_cmd "$server" "journalctl -u mcp-cms --no-pager -n 30"

echo ""
echo "=== 服务状态详情 ==="
$ssh_cmd "$server" "systemctl status mcp-cms --no-pager -l"
