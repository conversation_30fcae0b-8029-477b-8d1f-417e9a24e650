#!/bin/bash

# MCP-CMS 生产环境重新初始化脚本（简化版）
# 用于已成功初始化过的环境的快速重新部署

set -e

# 从配置文件加载服务器配置（如果存在）
load_config() {
    local config_file="${1:-server_config.conf}"
    if [ -f "$config_file" ]; then
        print_info "加载配置文件: $config_file"
        source "$config_file"
    fi
}

# 服务器配置函数
get_server() {
    case $1 in
        "qd") echo "${SERVER_QD:-<EMAIL>}";;
        "sha") echo "${SERVER_SHA:-<EMAIL>}";;
        "tyo") echo "${SERVER_TYO:-<EMAIL>}";;
        "hkg") echo "${SERVER_HKG:-<EMAIL>}";;
    esac
}

get_remote_path() {
    case $1 in
        "qd") echo "${REMOTE_PATH_QD:-/home/<USER>/mcp-cms/}";;
        "sha") echo "${REMOTE_PATH_SHA:-/home/<USER>/mcp-cms/}";;
        "tyo") echo "${REMOTE_PATH_TYO:-/root/mcp-cms/}";;
        "hkg") echo "${REMOTE_PATH_HKG:-/root/mcp-cms/}";;
    esac
}

get_service_name() {
    case $1 in
        "qd"|"sha"|"tyo"|"hkg") echo "mcp-cms";;
    esac
}

# 通用配置
LOCAL_PATH="/Users/<USER>/x-code-files/mcp-cms"
PASSWORD="2929!lxj#LXJ"
HK_SSH_KEY="/Users/<USER>/Documents/pem-files/<EMAIL>"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 获取SSH连接命令
get_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p \"$PASSWORD\" ssh -o StrictHostKeyChecking=no"
    fi
}

# 获取rsync SSH命令
get_rsync_ssh_cmd() {
    local server_key="$1"
    if [[ "$server_key" == "hkg" ]]; then
        echo "ssh -i $HK_SSH_KEY -o StrictHostKeyChecking=no"
    else
        echo "sshpass -p '$PASSWORD' ssh -o StrictHostKeyChecking=no"
    fi
}

# 检查SSH连接
check_ssh_connection() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local ssh_cmd=$(get_ssh_cmd "$server_key")

    print_info "[$server_key] 检查SSH连接到 $server..."
    if $ssh_cmd -o ConnectTimeout=10 "$server" "exit" 2>/dev/null; then
        print_success "[$server_key] SSH连接成功"
        return 0
    else
        print_error "[$server_key] SSH连接失败"
        return 1
    fi
}

# 停止服务
stop_services() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    local ssh_cmd=$(get_ssh_cmd "$server_key")

    print_info "[$server_key] 停止服务..."
    if [[ "$server_key" == "hkg" ]]; then
        $ssh_cmd "$server" "sudo systemctl stop $service_name 2>/dev/null || true" 2>/dev/null || true
    else
        $ssh_cmd "$server" "echo '$PASSWORD' | sudo -S systemctl stop $service_name 2>/dev/null || true" 2>/dev/null || true
    fi
}

# 传输项目文件
transfer_project_files() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 传输项目文件到 $server..."
    
    # 使用rsync传输文件（排除不需要的文件）
    local rsync_ssh_cmd=$(get_rsync_ssh_cmd "$server_key")
    rsync -avz --progress \
        --exclude-from=/tmp/rsync_exclude_reinit \
        -e "$rsync_ssh_cmd" \
        "$LOCAL_PATH/" \
        "$server:$remote_path/" || {
        print_error "[$server_key] 文件传输失败"
        return 1
    }

    # 设置文件权限
    local ssh_cmd=$(get_ssh_cmd "$server_key")
    $ssh_cmd "$server" \
        "find $remote_path -name '*.py' -exec chmod +x {} \\; && \
         chmod +x $remote_path/*.sh 2>/dev/null || true"
    
    print_success "[$server_key] 项目文件传输完成"
    return 0
}

# 快速重建虚拟环境（使用已知可工作的配置）
rebuild_virtual_environment() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local remote_path=$(get_remote_path "$server_key")
    
    print_info "[$server_key] 快速重建虚拟环境..."
    
    # 设置 uv 路径
    local uv_path="export PATH=\$PATH:~/.local/bin"
    
    # 删除现有虚拟环境
    local ssh_cmd=$(get_ssh_cmd "$server_key")
    $ssh_cmd "$server" \
        "cd $remote_path && rm -rf .venv __pycache__ .pytest_cache 2>/dev/null || true"

    # 重建虚拟环境（使用简化流程，因为已知环境可工作）
    local setup_output
    setup_output=$($ssh_cmd "$server" \
        "cd $remote_path && $uv_path && uv venv .venv && uv sync" 2>&1)
    local exit_code=$?
    
    if [[ $exit_code -eq 0 ]]; then
        print_success "[$server_key] 虚拟环境重建成功"
    else
        print_error "[$server_key] 虚拟环境重建失败"
        echo "错误详情："
        echo "$setup_output"
        return 1
    fi
    
    return 0
}

# 启动服务
start_services() {
    local server_key="$1"
    local server=$(get_server "$server_key")
    local service_name=$(get_service_name "$server_key")
    local ssh_cmd=$(get_ssh_cmd "$server_key")

    print_info "[$server_key] 启动服务..."
    if [[ "$server_key" == "hkg" ]]; then
        if $ssh_cmd "$server" "sudo systemctl start $service_name" 2>/dev/null; then
            # 等待启动完成
            sleep 3
            local status
            status=$($ssh_cmd "$server" "systemctl is-active $service_name" 2>/dev/null || echo "unknown")

            if [[ "$status" == "active" ]]; then
                print_success "[$server_key] 服务启动成功"
            else
                print_warning "[$server_key] 服务状态异常: $status"
            fi
        else
            print_error "[$server_key] 服务启动失败"
        fi
    else
        if $ssh_cmd "$server" "echo '$PASSWORD' | sudo -S systemctl start $service_name" 2>/dev/null; then
            # 等待启动完成
            sleep 3
            local status
            status=$($ssh_cmd "$server" "systemctl is-active $service_name" 2>/dev/null || echo "unknown")

            if [[ "$status" == "active" ]]; then
                print_success "[$server_key] 服务启动成功"
            else
                print_warning "[$server_key] 服务状态异常: $status"
            fi
        else
            print_error "[$server_key] 服务启动失败"
        fi
    fi
}

# 创建排除文件列表
create_exclude_file() {
    cat > /tmp/rsync_exclude_reinit << EOF
.env
.env.*
*.md
.venv/
__pycache__/
*/__pycache__/
**/__pycache__/
*.pyc
*.pyo
.git/
.gitignore
.DS_Store
node_modules/
.pytest_cache/
socket
*.xlsx
*.csv
*.json
example_*.py
test_*.py
*_backup.py
debug_*.py
check_*.py
scripts/
*.sh
AI_*.md
*_GUIDE.md
server_config.conf.example
update
redeploy
status
cache/
EOF
}

# 显示使用说明
show_usage() {
    echo "MCP-CMS 生产环境重新初始化脚本（简化版）"
    echo ""
    echo "⚡ 快速重新部署：适用于已成功初始化过的环境"
    echo "   - 停止服务"
    echo "   - 传输最新代码"
    echo "   - 重建虚拟环境"
    echo "   - 重启服务"
    echo ""
    echo "使用方法: $0 [配置文件] [选项]"
    echo ""
    echo "选项："
    echo "  --servers SERVER_LIST    指定服务器（用逗号分隔，如：qd,sha）"
    echo "  --skip-env-rebuild       跳过虚拟环境重建（仅传输代码）"
    echo "  --help                   显示此帮助信息"
    echo ""
    echo "示例："
    echo "  $0                       # 重新初始化所有服务器"
    echo "  $0 --servers qd,sha      # 仅重新初始化指定服务器"
    echo "  $0 --skip-env-rebuild    # 仅传输代码，不重建环境"
    echo ""
}

# 主重新初始化函数
main() {
    local config_file=""
    local target_servers=("qd" "sha" "tyo" "hkg")
    local skip_env_rebuild=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --servers)
                IFS=',' read -ra target_servers <<< "$2"
                shift 2
                ;;
            --skip-env-rebuild)
                skip_env_rebuild=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                if [[ -z "$config_file" && -f "$1" ]]; then
                    config_file="$1"
                elif [[ -z "$config_file" ]]; then
                    config_file="$1"
                fi
                shift
                ;;
        esac
    done
    
    load_config "$config_file"
    
    echo "⚡ 开始MCP-CMS生产环境快速重新初始化..."
    echo "🎯 目标服务器: ${target_servers[*]}"
    echo "📍 本地路径: $LOCAL_PATH"
    if [[ "$skip_env_rebuild" == true ]]; then
        echo "⏩ 跳过虚拟环境重建"
    fi
    echo ""
    
    if [ ! -d "$LOCAL_PATH" ]; then
        print_error "本地路径不存在: $LOCAL_PATH"
        exit 1
    fi
    
    if ! command -v sshpass &> /dev/null; then
        print_error "sshpass未安装，请先安装: brew install sshpass"
        exit 1
    fi
    
    create_exclude_file
    
    local failed_servers=()
    
    # 第一步：检查连接
    print_info "第1步: 检查服务器连接..."
    for server_key in "${target_servers[@]}"; do
        if ! check_ssh_connection "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器连接失败: ${failed_servers[*]}"
        exit 1
    fi
    
    # 第二步：停止服务
    print_info "第2步: 停止所有服务..."
    for server_key in "${target_servers[@]}"; do
        stop_services "$server_key"
    done
    
    # 第三步：传输文件
    print_info "第3步: 传输项目文件..."
    failed_servers=()
    for server_key in "${target_servers[@]}"; do
        if ! transfer_project_files "$server_key"; then
            failed_servers+=("$server_key")
        fi
    done
    
    if [ ${#failed_servers[@]} -gt 0 ]; then
        print_error "以下服务器文件传输失败: ${failed_servers[*]}"
        exit 1
    fi
    
    # 第四步：重建虚拟环境（可选）
    if [[ "$skip_env_rebuild" != true ]]; then
        print_info "第4步: 重建虚拟环境..."
        for server_key in "${target_servers[@]}"; do
            rebuild_virtual_environment "$server_key"
        done
    else
        print_info "第4步: 跳过虚拟环境重建"
    fi
    
    # 第五步：启动服务
    print_info "第5步: 启动所有服务..."
    for server_key in "${target_servers[@]}"; do
        start_services "$server_key"
    done
    
    # 清理临时文件
    rm -f /tmp/rsync_exclude_reinit
    
    echo ""
    print_success "🎉 重新初始化完成!"
    echo ""
    print_info "服务器状态:"
    for server_key in "${target_servers[@]}"; do
        local server=$(get_server "$server_key")
        local service_name=$(get_service_name "$server_key")
        local ssh_cmd=$(get_ssh_cmd "$server_key")
        local status
        status=$($ssh_cmd "$server" "systemctl is-active $service_name" 2>/dev/null || echo "unknown")

        if [[ "$status" == "active" ]]; then
            echo "✅ [$server_key] $server - 运行中"
        else
            echo "⚠️  [$server_key] $server - 状态: $status"
        fi
    done
    echo ""
}

trap 'print_error "脚本执行被中断"; rm -f /tmp/rsync_exclude_reinit; exit 1' INT TERM

main "$@" 