#!/bin/bash

# MySQL到PostgreSQL快速迁移脚本
# 自动执行表结构迁移和数据迁移的完整流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
check_python_env() {
    log_info "检查Python环境..."
    
    if ! command -v python &> /dev/null; then
        log_error "Python未安装或不在PATH中"
        exit 1
    fi
    
    # 检查依赖
    if ! python -c "import psycopg2" 2>/dev/null; then
        log_warning "PostgreSQL驱动未安装，正在安装..."
        uv sync
    fi
    
    log_success "Python环境检查完成"
}

# 测试数据库连接
test_connections() {
    log_info "测试数据库连接..."
    
    # 测试MySQL连接
    log_info "测试MySQL连接..."
    if ! python -c "from utils.basic.data_conn_unified import execute_mysql_query, MYSQL_DB_MCP; execute_mysql_query('SELECT 1', database=MYSQL_DB_MCP)" 2>/dev/null; then
        log_error "MySQL连接失败，请检查.env配置"
        exit 1
    fi
    log_success "MySQL连接正常"
    
    # 测试PostgreSQL连接
    log_info "测试PostgreSQL连接..."
    if ! python -c "from utils.basic.postgres_conn import test_postgres_connection; assert test_postgres_connection()" 2>/dev/null; then
        log_error "PostgreSQL连接失败，请检查.env配置"
        exit 1
    fi
    log_success "PostgreSQL连接正常"
}

# 迁移表结构
migrate_schema() {
    log_info "开始表结构迁移..."
    
    # 先预览
    log_info "预览表结构迁移..."
    python migrate_mysql_to_postgres.py --dry-run
    
    # 询问用户是否继续
    echo
    read -p "是否继续执行表结构迁移？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "用户取消表结构迁移"
        exit 0
    fi
    
    # 执行迁移
    log_info "执行表结构迁移..."
    if python migrate_mysql_to_postgres.py --drop-existing; then
        log_success "表结构迁移完成"
    else
        log_error "表结构迁移失败"
        exit 1
    fi
}

# 迁移数据
migrate_data() {
    log_info "开始数据迁移..."
    
    # 显示预估信息
    log_info "数据迁移预估信息："
    echo "  - tokens: ~10,000 行 (预计 1-2 分钟)"
    echo "  - t_scheduler_check_log: ~1,000 行 (预计 <1 分钟)"
    echo "  - t_job_details: ~150,000 行 (预计 10-15 分钟)"
    echo "  - t_booking_details: ~500,000 行 (预计 30-45 分钟)"
    echo "  - 总计预估时间: 45-60 分钟"
    echo
    
    # 询问用户是否继续
    read -p "是否继续执行数据迁移？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_warning "用户取消数据迁移"
        exit 0
    fi
    
    # 执行数据迁移
    log_info "开始数据迁移，请耐心等待..."
    log_info "您可以在另一个终端运行以下命令监控进度："
    log_info "  watch -n 10 'python migrate_mysql_data_to_postgres.py --progress'"
    log_info "  tail -f data_migration.log"
    echo
    
    if python migrate_mysql_data_to_postgres.py; then
        log_success "数据迁移完成"
    else
        log_error "数据迁移失败，请检查日志文件 data_migration.log"
        exit 1
    fi
}

# 验证数据
validate_data() {
    log_info "验证迁移后的数据..."
    
    if python migrate_mysql_data_to_postgres.py --validate-only; then
        log_success "数据验证通过"
    else
        log_error "数据验证失败"
        exit 1
    fi
}

# 显示迁移总结
show_summary() {
    log_success "迁移完成！"
    echo
    log_info "迁移总结："
    python migrate_mysql_data_to_postgres.py --progress
    echo
    log_info "后续步骤："
    echo "  1. 更新应用程序配置以使用PostgreSQL"
    echo "  2. 进行性能测试"
    echo "  3. 建立备份策略"
    echo "  4. 配置监控和告警"
    echo
    log_info "相关文件："
    echo "  - 迁移日志: data_migration.log"
    echo "  - 进度文件: migration_progress.json"
    echo "  - 详细文档: DATA_MIGRATION_GUIDE.md"
}

# 主函数
main() {
    echo "========================================"
    echo "MySQL到PostgreSQL迁移脚本"
    echo "========================================"
    echo
    
    # 解析命令行参数
    SKIP_SCHEMA=false
    SKIP_DATA=false
    RESUME=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-schema)
                SKIP_SCHEMA=true
                shift
                ;;
            --skip-data)
                SKIP_DATA=true
                shift
                ;;
            --resume)
                RESUME=true
                shift
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo
                echo "选项:"
                echo "  --skip-schema    跳过表结构迁移"
                echo "  --skip-data      跳过数据迁移"
                echo "  --resume         从中断处恢复数据迁移"
                echo "  --help, -h       显示此帮助信息"
                echo
                echo "示例:"
                echo "  $0                    # 完整迁移"
                echo "  $0 --skip-schema     # 只迁移数据"
                echo "  $0 --resume          # 恢复中断的迁移"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
    
    # 执行迁移步骤
    check_python_env
    test_connections
    
    if [[ "$SKIP_SCHEMA" != true ]]; then
        migrate_schema
    else
        log_info "跳过表结构迁移"
    fi
    
    if [[ "$SKIP_DATA" != true ]]; then
        if [[ "$RESUME" == true ]]; then
            log_info "从中断处恢复数据迁移..."
            if python migrate_mysql_data_to_postgres.py --resume; then
                log_success "数据迁移恢复完成"
            else
                log_error "数据迁移恢复失败"
                exit 1
            fi
        else
            migrate_data
        fi
        
        validate_data
    else
        log_info "跳过数据迁移"
    fi
    
    show_summary
}

# 捕获中断信号
trap 'log_warning "迁移被中断，可以使用 --resume 参数恢复"; exit 1' INT TERM

# 运行主函数
main "$@"
