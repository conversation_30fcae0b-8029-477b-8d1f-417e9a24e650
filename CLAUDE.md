# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is MCP-CMS, a Python-based enterprise content management system that integrates with FastAPI, PostgreSQL, MySQL, and Firebird databases. The system provides MCP (Model Context Protocol) server functionality with data export, AI analysis, and system monitoring capabilities.

## Architecture

### Core Components
- **mcp_server_cms.py**: Main MCP server entry point with FastAPI integration
- **utils/fastapi_apps/**: FastAPI applications for web services
- **utils/database/**: Database connectivity modules (MySQL, Firebird, PostgreSQL)
- **utils/basic/**: Core utilities (logging, caching, data export, scheduling)

### Database Architecture
- **Primary databases**: MySQL (Pro2 system), Firebird (legacy), PostgreSQL (analytics)
- **Connection management**: Unified connection pooling with SSH tunnel support
- **Data flow**: Extract → Transform → Export (Excel/CSV) → OSS storage

### Key Modules
- **data_conn_unified.py**: Centralized database connection management
- **optimized_export.py**: Data export to Excel/CSV with OSS integration
- **profit_data_scheduler.py**: Scheduled data processing tasks
- **data_cache_manager.py**: Redis-based caching layer

## Development Commands

### Environment Setup
```bash
# Install dependencies using uv (faster than pip)
uv sync

# Activate virtual environment
source .venv/bin/activate  # Linux/macOS
# or
.venv\Scripts\activate     # Windows
```

### Running the Application
```bash
# Start MCP server (main entry point)
python mcp_server_cms.py

# Start FastAPI app directly
uvicorn utils.fastapi_apps.fastapi_cms_simplified:app --reload

# Run with custom configuration
python mcp_server_cms.py --config custom_config.conf
```

### Development Scripts
The `scripts/` directory contains deployment and maintenance scripts:

```bash
# Daily code updates (most common)
./scripts/transfer_fixed.sh

# Full redeployment 
./scripts/reinit.sh

# Service management
./scripts/start_all_services.sh
./scripts/stop_all_services.sh
./scripts/check_all_services.sh

# Check logs
./scripts/check_service_logs.sh
```

### Database Operations
```bash
# Test database connections
python -m utils.basic.data_conn_unified

# Run profit data scheduler manually
python -m utils.basic.profit_data_scheduler

# Export data manually
python -m utils.basic.optimized_export
```

## Configuration

### Environment Variables
Configure via `.env` file or environment:
- Database credentials (MySQL, Firebird, PostgreSQL)
- SSH tunnel settings for remote database access
- OSS (Object Storage Service) configuration
- Redis cache settings
- Email notification settings

### Time-based Execution
The system uses **config.py** for time-based job scheduling:
- Default execution: 20:00-08:00 daily
- Configurable per weekday in `WEEKDAY_EXECUTION_TIMES`
- Supports multiple time ranges per day

## Key Patterns

### Database Connections
- All database operations go through `utils/basic/data_conn_unified.py`
- Uses connection pooling and automatic retry logic
- SSH tunnels for secure remote database access
- Supports both direct and tunneled connections

### Data Export Flow
1. Query data from source databases (MySQL/Firebird)
2. Apply column mapping and transformations
3. Export to Excel/CSV format
4. Upload to OSS storage
5. Cache results in Redis

### Error Handling
- Comprehensive retry mechanisms for database operations
- Custom logging with rotation and filtering
- Email notifications for critical failures
- Graceful degradation for non-critical services

## Testing

No formal test framework detected. Manual testing approach:
- Use `scripts/check_all_services.sh` to verify service health
- Monitor logs in `logs/` directory
- Test database connections using connection modules directly

## Deployment

### Production Deployment
Uses script-based deployment to multiple servers:
- **qd**: qd.cmsgroup.com.cn (domestic)
- **sha**: sh.cmsgroup.com.cn (domestic) 
- **tyo**: jp-server.cmslogistics.info (international)
- **hkg**: hk-aliyun.cmslogistics.info (international)

### Service Management
- Runs as systemd service named `mcp-cms`
- Uses uv for Python package management
- Logs stored in `logs/` directory
- Process monitoring via Prometheus metrics

## Important Notes

- Uses Aliyun PyPI mirror for faster package installation
- SSH tunnels required for database access from local development
- Time-sensitive operations respect configured execution windows
- Extensive logging and monitoring for production stability