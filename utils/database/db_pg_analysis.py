# -*- coding: utf-8 -*-
# 业务数据的分析, 包括对特例情况的修正
"""
- 通过对PostgreSQL数据库表t_job_details和表t_booking_details的分析, 获取到业务分析数据 --> 特例: 对于pro2_system_id的值为86021的业务, Job表中的如下字段数据, 需要通过对Booking表(特例修正后的)的汇总数据进行分析获得:
    1. 'all_nominated_count',      # 全部指定货票数 --> 特例修正后的is_free_hand字段为0的booking的'business_no'数量(count)
    2. 'all_nominated_rt',         # 全部指定货RT --> 特例修正后的is_free_hand字段为0的booking的'lcl_rt'合计值(sum)
- 参考脚本 utils/basic/profit_data_scheduler.py 中对于t_job_details和t_booking_details表的定义和中文名称含义映射
- Job & Booking 表的pro2_system_id的数值, 映射了不同给的分公司的业务数据: 86532-青岛(港口代码QDO), 86021-上海(港口代码SHA), 852-香港(港口代码HKG), 8103-东京(港口代码TKY)

对于Booking业务, 需要分析的基础数据包括: 
    'business_type_name',    # 业务类型
    'job_date',             # 工作档日期
    'job_file_no',          # 工作档编号
    'business_no',          # 订舱提单编号
    'shipper_name',         # 客户名称
    'vessel',               # 船名
    'voyage',               # 航次 
    'pol_code',             # 提单起运地 --> 如果提单起运地为空, 则使用sailing_pol的值
    'pod_code',             # 提单卸货地 --> 如果提单卸货地为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'service_mode',         # 服务模式
    'lcl_rt',               # 拼箱RT
    'teu',                  # TEU
    'air_weight',           # 空运重量
    'income',               # 收入
    'cost',                 # 成本
    'profit',               # 利润
    'is_transhipment',      # 是否转运
    'transhipment_profit',  # 转运利润
    'is_free_hand',         # 自揽货 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_name',        # 业务员 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'salesman_dept_name',   # 营业员部门 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'nomi_agent_name',      # 指定货代理 --> 特例: 对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断
    'operator_name',        # 操作员
    'operator_dept_name',   # 操作部门
    'coloader_name',        # Coloader名称
    'job_handling_agent_name'  # 工作档代理

对于Job业务, 需要分析的基础数据包括: 
    'business_type_name',  # 业务类型
    'job_date',           # 工作档日期
    'job_file_no',        # 工作档编号
    'vessel',             # 船名
    'voyage',             # 航次
    'pol_code',           # 起运港
    'pod_code',           # 卸货港 --> 如果卸货港为空, 则使用pro2_system_id的值对应的港口代码(特例:pro2_system_id的值为86532除外, 因为86532的卸货港需要通过'操作部门'判断)
    'bk_count',           # 订舱数
    'bl_count',           # 提单数
    'total_rt',           # 计费吨
    'total_teu',          # TEU
    'income',             # 收入
    'cost',               # 成本
    'profit',             # 利润
    'transhipment_count', # 转运票数
    'transhipment_profit', # 转运利润
    'operator_name',      # 操作员
    'job_handling_agent_name',  # 工作档代理
    'all_nominated_count',      # 全部指定货票数
    'all_nominated_rt',         # 全部指定货RT
    'port_agent_nominated_count', # 港代指定货票数
    'port_agent_nominated_rt',    # 港代指定货RT
    'is_consol',               # 是否集拼
    'consol_20_count',        # 20集拼量
    'consol_40_count',        # 40集拼量
    'operator_dept_name',     # 操作部门
    'is_op_finished',         # 操作完成
    'is_checked'              # 审核状态

** 对于特例情况的值的判断规则: **
对于pro2_system_id的值为86532的业务, pod_code(卸货港)的值为空时, 需要通过'操作部门'判断对应的卸货港的规则:
    1. 如果'操作部门'以'QD/'开头, 则pod_code的值视为'QDO'
    2. 如果'操作部门'以'GZ/'开头, 则pod_code的值视为'GZG'
    3. 如果'操作部门'以'NB/'开头, 则pod_code的值视为'NBO'
    4. 如果'操作部门'以'XM/'开头, 则pod_code的值视为'XMN'

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'is_free_hand'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'is_free_hand'字段的值视为0
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'is_free_hand'字段的值视为0
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'is_free_hand'字段的值视为1

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'salesman_dept_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'salesman_dept_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'salesman_dept_name'字段的值视为空值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'salesman_dept_name'字段的值视为不变

对于pro2_system_id的值为86021的业务, 需要单独使用'salesman_dept_name'字段判断'nomi_agent_name'字段的视为值:
    1. 如果'is_free_hand'字段的值为0, 则'nomi_agent_name'字段的值视为不变
    2. 如果'is_free_hand'字段的值为1, 则:
        2.1 如果'salesman_dept_name'字段的值为'指定货业务', 则'nomi_agent_name'字段的视为值:
            2.1.1 如果'salesman_name'字段的值为包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值删除'指定货'或者'指定'字样后的值
            2.1.2 如果'salesman_name'字段的值为不包含'指定货'或者'指定'字样, 则'nomi_agent_name'字段的值视为'salesman_name'字段的值
        2.2 如果'salesman_dept_name'字段的值不为'指定货业务', 则'nomi_agent_name'字段的值视为空值

"""

import asyncio
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Dict, Any, Optional
import sys
import os
import traceback
from collections import defaultdict
import pandas as pd

# 使用PostgreSQL连接模块替代MySQL
from utils.basic.data_conn_fb_pg import (
    get_postgres_connection, 
    execute_postgres_query_async,
    connect_postgres_async,
    PG_DB_CMSDATA
)
from utils.basic.data_cache_manager import async_cached_data_function

# 生产环境优化的路径处理
def _setup_project_path():
    """
    设置项目根目录到Python路径中
    适用于开发环境和生产环境（Ubuntu服务器）
    """
    try:
        # 获取当前文件的绝对路径
        current_file = os.path.abspath(__file__)
        
        # 计算项目根目录（向上两级目录）
        project_root = os.path.dirname(os.path.dirname(current_file))
        
        # 验证项目根目录是否存在utils目录
        if os.path.exists(os.path.join(project_root, 'utils')):
            # 只有在路径不存在时才添加，避免重复
            if project_root not in sys.path:
                sys.path.insert(0, project_root)  # 使用insert(0)确保优先级
        else:
            # 如果找不到utils目录，使用环境变量或当前工作目录
            fallback_root = os.getenv('MCP_CMS_ROOT', os.getcwd())
            if fallback_root not in sys.path:
                sys.path.insert(0, fallback_root)
    except Exception as e:
        # 生产环境的错误处理
        print(f"Warning: Failed to setup project path: {e}")
        # 使用当前工作目录作为后备方案
        if os.getcwd() not in sys.path:
            sys.path.insert(0, os.getcwd())

# 执行路径设置
_setup_project_path()


def example_usage_unified_format():
    """
    新的统一返回格式使用示例 - PostgreSQL版本
    
    所有分析类函数(analysis_xxx)和提取类函数现在都返回统一格式:
    {
        "json_data": 原始JSON数据,
        "pd_data": pandas.DataFrame格式数据(按columns_info顺序排列),
        "metadata": {
            "data_type": "数据类型标识",
            "query_info": "查询信息",
            "columns_info": "列说明字典(决定DataFrame列顺序)"
        }
    }
    
    主要优化内容:
    1. DataFrame列顺序严格按照columns_info的key顺序排列
    2. 只包含columns_info中存在的字段
    3. get_sea_air_profit_from_tokens_table_cached函数新增nomi_agent_name字段
    4. 对86021分公司特例处理：指定货时清空salesman字段，使用nomi_agent_name
    
    使用示例:
    
    # 1. 调用分析函数
    result = await analysis_booking_by_month_data('2024-01-01', 3, 86021)
    
    # 2. 获取DataFrame数据进行分析(已按指定顺序排列)
    df = result['pd_data']
    print(df.head())
    print(df.columns.tolist())  # 查看列顺序
    print(df.groupby('business_type')['profit'].sum())
    
    # 3. 获取原始JSON数据
    json_data = result['json_data']
    
    # 4. 查看元数据信息
    metadata = result['metadata']
    print(f"数据类型: {metadata['data_type']}")
    print(f"列说明: {metadata['columns_info']}")
    
    # 5. 特殊功能：86021指定货处理示例
    booking_result = await get_sea_air_profit_from_tokens_table_cached('2024-01-01', '2024-01-31', 86021)
    booking_df = booking_result['pd_data']
    # 对于86021且is_freehand=0的记录，salesman_name/salesman_department为空，nomi_agent_name有值
    nominated_records = booking_df[booking_df['is_freehand'] == 0]
    print(nominated_records[['salesman_name', 'salesman_department', 'nomi_agent_name']].head())
    """
    pass

def convert_monthly_analysis_to_dataframe(data: List[Dict[str, Any]], analysis_type: str, columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将月度分析数据转换为DataFrame格式，按照columns_info顺序排列
    
    Args:
        data: 月度分析数据列表
        analysis_type: 分析类型 (booking/job)
        columns_info: 列信息字典，用于确定列顺序和过滤字段
        
    Returns:
        pd.DataFrame: 转换后的DataFrame
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for month_data in data:
        year = month_data.get('year')
        month = month_data.get('month')
        pro2_system_id = month_data.get('pro2_system_id')
        
        # 处理各业务类型数据
        business_types = [
            ('sea_export', month_data.get('sea_export_data', [])),
            ('sea_import', month_data.get('sea_import_data', [])),
            ('triangle_trade', month_data.get('triangle_trade_data', [])),
            ('air', month_data.get('air_data', []))
        ]
        
        for business_type, type_data in business_types:
            if type_data:
                for item in type_data:
                    row = {
                        'year': year,
                        'month': month,
                        'pro2_system_id': pro2_system_id,
                        'business_type': business_type,
                        'analysis_type': analysis_type
                    }
                    row.update(item)
                    rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_customer_analysis_to_dataframe(data: List[Dict[str, Any]], columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将客户分析数据转换为DataFrame格式，按照columns_info顺序排列
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for month_data in data:
        year = month_data.get('year')
        month = month_data.get('month')
        pro2_system_id = month_data.get('pro2_system_id')
        customer_name = month_data.get('customer_name')
        
        for pod_data in month_data.get('data', []):
            row = {
                'year': year,
                'month': month,
                'pro2_system_id': pro2_system_id,
                'customer_name': customer_name
            }
            row.update(pod_data)
            rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_entity_analysis_to_dataframe(data: List[Dict[str, Any]], entity_type: str, columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将实体分析数据转换为DataFrame格式（适用于客户、代理、港口等），按照columns_info顺序排列
    
    Args:
        data: 实体分析数据列表
        entity_type: 实体类型 (customer/agent/port/consol_line)
        columns_info: 列信息字典，用于确定列顺序和过滤字段
    """
    if not data:
        return pd.DataFrame()
    
    rows = []
    for entity_data in data:
        # 获取实体标识字段
        entity_fields = {}
        if entity_type == 'customer':
            entity_fields['customer_name'] = entity_data.get('customer_name')
        elif entity_type == 'agent':
            entity_fields['nomi_agent_name'] = entity_data.get('nomi_agent_name')
        elif entity_type == 'port':
            entity_fields['pod_code'] = entity_data.get('pod_code')
        elif entity_type == 'consol_line':
            entity_fields['consol_line'] = entity_data.get('consol_line')
        
        pro2_system_id = entity_data.get('pro2_system_id')
        
        for month_data in entity_data.get('data', []):
            row = {
                'pro2_system_id': pro2_system_id,
                'entity_type': entity_type
            }
            row.update(entity_fields)
            row.update(month_data)
            rows.append(row)
    
    df = pd.DataFrame(rows)
    
    # 只保留columns_info中存在的字段，并按照columns_info的顺序排列
    if not df.empty and columns_info:
        available_columns = [col for col in columns_info.keys() if col in df.columns]
        df = df[available_columns]
    
    return df

def convert_records_to_dataframe(records: List[Dict[str, Any]], columns_info: Dict[str, str]) -> pd.DataFrame:
    """
    将记录数据转换为DataFrame格式，按照columns_info的key顺序排列列
    
    Args:
        records: 记录数据列表
        columns_info: 列信息字典，决定DataFrame的列顺序和包含的列
        
    Returns:
        pd.DataFrame: 转换后的DataFrame，列顺序按照columns_info的key顺序
    """
    if not records:
        return pd.DataFrame(columns=list(columns_info.keys()) if columns_info else [])
    
    # 只保留columns_info中存在的字段
    filtered_data = []
    for record in records:
        if columns_info:
            filtered_record = {key: record.get(key) for key in columns_info.keys()}
        else:
            filtered_record = record
        filtered_data.append(filtered_record)
    
    # 创建DataFrame，列顺序按照columns_info的key顺序
    if columns_info:
        df = pd.DataFrame(filtered_data, columns=list(columns_info.keys()))
    else:
        df = pd.DataFrame(filtered_data)
    
    return df


def apply_basic_data_cleaning(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用基本数据清理逻辑（轻量级版本，用于数据提取阶段）
    只进行必要的字段映射和基本数据清理，不进行复杂的业务逻辑转换

    Args:
        record: 数据记录

    Returns:
        清理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')

    # 通用处理：为所有系统设置is_freehand字段（兼容不同字段名）
    if 'is_freehand' not in processed_record:
        # 优先使用is_free_hand字段，因为这是SQL查询返回的字段名
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        processed_record['is_freehand'] = original_is_freehand

    # 处理提单起运地特例：如果提单起运地为空，则使用工作档起运地的值
    if not processed_record.get('bill_pol') or processed_record.get('bill_pol') == '':
        processed_record['bill_pol'] = processed_record.get('job_pol', '')

    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('bill_pod') or record.get('bill_pod') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['bill_pod'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['bill_pod'] = 'GZG'
            elif operator_dept.startswith('NB/'):
                processed_record['bill_pod'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['bill_pod'] = 'XMN'

    # 为所有记录添加基本的 nomi_agent_name 字段（如果不存在）
    if 'nomi_agent_name' not in processed_record:
        processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''

    return processed_record

def apply_special_case_processing(record: Dict[str, Any]) -> Dict[str, Any]:
    """
    应用特例处理逻辑
    
    Args:
        record: 数据记录
        
    Returns:
        处理后的数据记录
    """
    processed_record = record.copy()
    pro2_system_id = record.get('pro2_system_id')
    
    # 通用处理：为所有系统设置is_freehand字段（兼容不同字段名）
    if 'is_freehand' not in processed_record:
        # 优先使用is_free_hand字段，因为这是SQL查询返回的字段名
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        processed_record['is_freehand'] = original_is_freehand
    
    # 处理提单起运地特例：如果提单起运地为空，则使用工作档起运地的值
    if not processed_record.get('bill_pol') or processed_record.get('bill_pol') == '':
        processed_record['bill_pol'] = processed_record.get('job_pol', '')
    
    # 处理 pro2_system_id = 86532 的特例
    if pro2_system_id == 86532:
        # 处理 pod_code 为空的情况
        if not record.get('bill_pod') or record.get('bill_pod') == '':
            operator_dept = record.get('operator_department', '')
            if operator_dept.startswith('QD/'):
                processed_record['bill_pod'] = 'QDO'
            elif operator_dept.startswith('GZ/'):
                processed_record['bill_pod'] = 'GZG'
            elif operator_dept.startswith('NB/'):
                processed_record['bill_pod'] = 'NBO'
            elif operator_dept.startswith('XM/'):
                processed_record['bill_pod'] = 'XMN'
    
    # 处理其他分公司的卸货港为空特例：使用pro2_system_id对应的港口代码
    if not processed_record.get('bill_pod') or processed_record.get('bill_pod') == '':
        if pro2_system_id == 86021:
            processed_record['bill_pod'] = 'SHA'
        elif pro2_system_id == 852:
            processed_record['bill_pod'] = 'HKG'
        elif pro2_system_id == 8103:
            processed_record['bill_pod'] = 'TKY'
    
    # 处理 pro2_system_id = 86021 的特例
    if pro2_system_id == 86021:
        # 兼容不同的字段名称 - SQL查询返回的字段名是is_free_hand
        original_is_freehand = record.get('is_free_hand', record.get('is_freehand', 0))
        # 兼容不同的字段名称
        salesman_dept_name = record.get('salesman_dept_name', '') or record.get('salesman_department', '')
        salesman_name = record.get('salesman_name', '')
        
        # 处理 is_freehand 字段
        if original_is_freehand == 0:
            processed_record['is_freehand'] = 0
        elif original_is_freehand == 1:
            if salesman_dept_name == '指定货业务':
                processed_record['is_freehand'] = 0
                # 对于从"指定货业务"部门转换来的指定货，清空salesman字段
                processed_record['salesman_name'] = None
                processed_record['salesman_department'] = None
                processed_record['salesman_dept_name'] = None
            else:
                processed_record['is_freehand'] = 1
        
        # 处理 nomi_agent_name 字段（为记录添加这个字段）
        # 对于指定货（is_freehand=0），需要正确设置nomi_agent_name
        if processed_record['is_freehand'] == 0:
            # 如果原始记录来自"指定货业务"部门，从salesman_name中提取代理名称
            if salesman_dept_name == '指定货业务':
                if salesman_name:
                    if '指定货' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定货', '').strip()
                    elif '指定' in salesman_name:
                        processed_record['nomi_agent_name'] = salesman_name.replace('指定', '').strip()
                    else:
                        processed_record['nomi_agent_name'] = salesman_name.strip()
                else:
                    processed_record['nomi_agent_name'] = ''
            else:
                # 对于不是来自"指定货业务"部门的86021指定货，使用job_handling_agent
                processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
        else:
            # 非指定货，清空nomi_agent_name
            processed_record['nomi_agent_name'] = ''
    
    # 为所有记录添加 nomi_agent_name 字段（如果不存在）
    if 'nomi_agent_name' not in processed_record:
        # 只有当is_freehand=0（指定货）时才设置nomi_agent_name
        if processed_record.get('is_freehand') == 0:
            # 修正：使用job_handling_agent作为指定货代理字段，因为bl_handling_agent字段基本为空
            processed_record['nomi_agent_name'] = record.get('job_handling_agent', '') or ''
        else:
            # 自揽货时，nomi_agent_name应为空
            processed_record['nomi_agent_name'] = ''
    
    return processed_record

def get_business_type_mapping() -> Dict[str, str]:
    """
    获取业务类型映射
    
    Returns:
        业务类型映射字典
    """
    return {
        '海运出口': 'sea_export',
        '海运进口': 'sea_import', 
        '海运三角贸易': 'triangle_trade',  # 修正：数据库中是"海运三角贸易"
        '空运': 'air'  # 修正：数据库中是"空运"而不是分出口和进口
    }

def calculate_aggregated_data(records: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    计算汇总数据
    
    Args:
        records: 数据记录列表
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "customer_count": 0,
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_profit": 0,
            "nomi_agent_count": 0,
        }
    
    # 客户数量（去重）
    customers = set()
    for record in records:
        if record.get('client_name'):
            customers.add(record['client_name'])
    
    # 指定货代理数量（去重）
    nomi_agents = set()
    nominated_records = []
    for record in records:
        if record.get('is_freehand') == 0:  # 指定货
            nominated_records.append(record)
            # 修正：正确过滤空字符串和None值
            nomi_agent_name = record.get('nomi_agent_name')
            if nomi_agent_name and nomi_agent_name.strip():
                nomi_agents.add(nomi_agent_name.strip())
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 指定货汇总
    nominated_rt = sum(float(record.get('lcl_rt', 0) or 0) for record in nominated_records)
    nominated_profit = sum(float(record.get('profit', 0) or 0) for record in nominated_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "customer_count": len(customers),
        "bkbl_count": len(records),
        "rt": sum(float(record.get('lcl_rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": sum(float(record.get('air_weight', 0) or 0) for record in records),
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": len(nominated_records),
        "all_nominated_rt": nominated_rt,
        "all_nominated_profit": nominated_profit,
        "nomi_agent_count": len(nomi_agents),
    }

def calculate_job_aggregated_data(records: List[Dict[str, Any]], business_type: str = '') -> Dict[str, Any]:
    """
    计算Job数据汇总
    
    Args:
        records: Job数据记录列表
        business_type: 业务类型，用于确定票数计算方式
        
    Returns:
        汇总数据字典
    """
    if not records:
        return {
            "bkbl_count": 0,
            "rt": 0,
            "teu": 0,
            "air_weight": 0,
            "income": 0,
            "cost": 0,
            "profit": 0,
            "profit_rate": 0,
            "all_nominated_count": 0,
            "all_nominated_rt": 0,
            "all_nominated_teu": 0,
            "consol_line_count": 0,
            "consol_rt": 0,
            "consol_20_count": 0,
            "consol_40_count": 0,
        }
    
    # 基础汇总
    total_income = sum(float(record.get('income', 0) or 0) for record in records)
    total_cost = abs(sum(float(record.get('cost', 0) or 0) for record in records))
    total_profit = sum(float(record.get('profit', 0) or 0) for record in records)
    
    # 票数计算：海运出口用bk_count，其他用bill_count
    if business_type == '海运出口':
        total_count = sum(int(record.get('bk_count', 0) or 0) for record in records)
    else:
        total_count = sum(int(record.get('bill_count', 0) or 0) for record in records)
    
    # 集拼航线数量计算：对于is_consolidation=1的记录，统计不同pol_code/pod_code组合的数量
    consol_lines = set()
    consol_records = []
    for record in records:
        if record.get('is_consolidation') == 1:
            consol_records.append(record)
            pol_code = record.get('pol_code', '')
            pod_code = record.get('pod_code', '')
            if pol_code and pod_code:
                consol_lines.add(f"{pol_code}/{pod_code}")
    
    # 集拼数据汇总
    consol_rt = sum(float(record.get('rt', 0) or 0) for record in consol_records)
    consol_20_count = sum(int(record.get('consolidation_20', 0) or 0) for record in consol_records)
    consol_40_count = sum(int(record.get('consolidation_40', 0) or 0) for record in consol_records)
    
    # 计算利润率
    profit_rate = (total_profit / total_income * 100) if total_income > 0 else 0
    
    return {
        "bkbl_count": total_count,
        "rt": sum(float(record.get('rt', 0) or 0) for record in records),
        "teu": sum(float(record.get('teu', 0) or 0) for record in records),
        "air_weight": 0,  # Job表中没有空运重量字段
        "income": total_income,
        "cost": total_cost,
        "profit": total_profit,
        "profit_rate": round(profit_rate, 2),
        "all_nominated_count": sum(int(record.get('nomi_count', 0) or 0) for record in records),
        "all_nominated_rt": sum(float(record.get('nomi_rt', 0) or 0) for record in records),
        "all_nominated_teu": 0,  # 需要从Booking表计算
        "consol_line_count": len(consol_lines),
        "consol_rt": consol_rt,
        "consol_20_count": consol_20_count,
        "consol_40_count": consol_40_count,
    }

# ====================
# 核心数据查询函数
# ====================

async def get_booking_details_from_tokens_table(begin_date: str, end_date: str, pro2_system_id: Optional[int] = None) -> Dict[str, Any]:
    """
    从 PostgreSQL 中的 t_booking_details 表根据时间周期查询海运空运损益并添加转运利润字段
    
    Args:
        begin_date: 开始日期, 格式为'YYYY-MM-DD'
        end_date: 结束日期, 格式为'YYYY-MM-DD'
        pro2_system_id: 分公司代码, 可选值为: 86532-QDO(青岛), 86021-SHA(上海), 852-HKG(香港), 8103-TKY(东京), 如果为空则提取所有分公司
        
    Returns:
        Dict[str, Any]: 包含数据列表和查询信息的字典
    """
    try:
        print(f"开始从PostgreSQL tokens表查询booking数据（含转运）: {begin_date} 到 {end_date}, 分公司: {pro2_system_id or '全部'}")
        
        # PostgreSQL版本的SQL查询，使用窗口函数优化性能
        sql = """
            SELECT 
                job_type_cn,
                job_date,
                job_no,
                bkbl_no,
                client_name,
                vessel,
                voyage,
                job_pol,
                bill_pol,
                bill_pod,
                service_mode,
                lcl_rt,
                teu,
                air_weight,
                income,
                cost,
                profit,
                transhipment_profit,
                total_business_profit,
                is_freehand,
                salesman_name,
                salesman_id,
                salesman_department,
                operator_name,
                operator_department,
                coloader_name,
                job_handling_agent,
                bl_handling_agent,
                is_transhipment,
                transhipment_id,
                bkbl_id,
                job_id,
                job_type_id,
                operator_id,
                pro2_system_id,
                data_hash
            FROM (
                SELECT 
                    t1.job_type_cn,
                    t1.job_date,
                    t1.job_no,
                    t1.bkbl_no,
                    t1.client_name,
                    t1.vessel,
                    t1.voyage,
                    t1.job_pol,
                    t1.bill_pol,
                    t1.bill_pod,
                    t1.service_mode,
                    t1.lcl_rt,
                    t1.teu,
                    t1.air_weight,
                    t1.income,
                    t1.cost,
                    t1.profit,
                    t1.transhipment_profit,
                    t1.total_business_profit,
                    t1.is_freehand,
                    t1.salesman_name,
                    t1.salesman_id,
                    t1.salesman_department,
                    t1.operator_name,
                    t1.operator_department,
                    t1.coloader_name,
                    t1.job_handling_agent,
                    t1.bl_handling_agent,
                    t1.is_transhipment,
                    t1.transhipment_id,
                    t1.bkbl_id,
                    t1.job_id,
                    t1.job_type_id,
                    t1.operator_id,
                    t1.pro2_system_id,
                    t1.data_hash,
                    ROW_NUMBER() OVER (
                        PARTITION BY t1.job_id, t1.bkbl_no, t1.pro2_system_id 
                        ORDER BY t1.id DESC
                    ) as rn
                FROM t_booking_details t1
                WHERE t1.job_date >= $1
                AND t1.job_date <= $2
        """
        
        # PostgreSQL需要日期对象而不是字符串
        from datetime import datetime
        begin_date_obj = datetime.strptime(begin_date, '%Y-%m-%d').date()
        end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        params = [begin_date_obj, end_date_obj]
        
        # 添加分公司过滤条件
        if pro2_system_id is not None:
            sql += " AND t1.pro2_system_id = $3"
            params.append(pro2_system_id)
        
        sql += """
            ) ranked_data
            WHERE rn = 1
            ORDER BY job_date, job_no, bkbl_no
        """
        
        print("执行PostgreSQL数据库查询...")
        # 执行查询
        raw_records = await execute_postgres_query_async(sql, tuple(params), fetch_all=True, database=PG_DB_CMSDATA)
        
        print(f"PostgreSQL数据库查询完成，获得 {len(raw_records)} 条记录")
        
        # 处理特例情况
        processed_records = []
        for record in raw_records:
            processed_record = apply_special_case_processing(record)
            processed_records.append(processed_record)
        
        print(f"从PostgreSQL tokens表查询booking数据完成（含转运）: {len(processed_records)} 条记录")
        
        return {
            'data': processed_records,
            'total_count': len(processed_records),
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'data_type': '全部订舱毛利数据（含转运）- 从PostgreSQL tokens表提取',
                'source_table': f'{PG_DB_CMSDATA}.t_booking_details',
                'pro2_system_id': pro2_system_id,
                'system_filter': f'分公司代码: {pro2_system_id}' if pro2_system_id else '全部分公司'
            }
        }
        
    except Exception as e:
        print(f"从PostgreSQL tokens表查询booking数据失败（含转运）: {e}")
        traceback.print_exc()
        return {
            'data': [],
            'total_count': 0,
            'query_info': {
                'date_range': f'{begin_date} 到 {end_date}',
                'error': str(e),
                'data_type': '查询失败',
                'source_table': f'{PG_DB_CMSDATA}.t_booking_details'
            }
        }


# 测试基本导入是否正常
def test_basic_import():
    """
    测试基本导入和连接功能
    """
    try:
        print("PostgreSQL Analysis module imported successfully")
        print(f"Using database: {PG_DB_CMSDATA}")
        return True
    except Exception as e:
        print(f"Import test failed: {e}")
        return False


if __name__ == "__main__":
    # 测试基本功能
    test_basic_import()