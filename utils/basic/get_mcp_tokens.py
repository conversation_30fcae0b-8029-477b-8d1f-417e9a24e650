# 获取MCP Client Token

import warnings
import asyncio

from utils.basic.data_conn_fb_pg import execute_postgres_query, execute_postgres_query_async

# 忽略ResourceWarning警告
warnings.filterwarnings("ignore", category=ResourceWarning)

def get_mcp_tokens(token_uuid:str, database:str=None):
    """
    检查给定的token是否存在于数据库中（同步版本）
    
    Args:
        token_uuid: 需要验证的token字符串
        database: 数据库名称 (PostgreSQL中由连接池管理)
        
    Returns:
        如果token有效，返回token信息；否则返回None
    """
    
    try:
        # PostgreSQL布尔类型查询（is_void为false）
        result = execute_postgres_query(
            query="SELECT token_uuid FROM tokens WHERE token_uuid = %s AND is_void = false",
            params=(token_uuid,),
            database=database
        )
        # 如果找到有效token，返回结果
        if result:
            return result
        return None
    except Exception as e:
        return None

async def get_mcp_tokens_async(token_uuid:str, database:str=None):
    """
    检查给定的token是否存在于数据库中（异步版本）
    
    Args:
        token_uuid: 需要验证的token字符串
        database: 数据库名称 (PostgreSQL中由连接池管理)
        
    Returns:
        如果token有效，返回token信息；否则返回None
    """
    
    try:
        # PostgreSQL布尔类型查询（is_void为false）
        result = await execute_postgres_query_async(
            query="SELECT token_uuid FROM tokens WHERE token_uuid = $1 AND is_void = false",
            params=(token_uuid,),
            database=database
        )
        # 如果找到有效token，返回结果
        if result:
            return result
        return None
    except Exception as e:
        return None

def verify_mcp_token(token_uuid:str):
    """
    验证MCP Token是否有效（同步版本）
    
    Args:
        token_uuid: 需要验证的token字符串
        
    Returns:
        bool: 如果token有效返回True，否则返回False
    """
    try:
        # 获取MCP Client Token (PostgreSQL数据库由连接池管理)
        tokens = get_mcp_tokens(token_uuid, database=None)
        if tokens:
            return True
        else:
            return False
    except Exception as e:
        return False

async def verify_mcp_token_async(token_uuid:str):
    """
    验证MCP Token是否有效（异步版本）
    
    Args:
        token_uuid: 需要验证的token字符串
        
    Returns:
        bool: 如果token有效返回True，否则返回False
    """
    try:
        # 获取MCP Client Token (PostgreSQL数据库由连接池管理)
        tokens = await get_mcp_tokens_async(token_uuid, database=None)
        if tokens:
            return True
        else:
            return False
    except Exception as e:
        return False

async def main():
    """主测试函数"""
    test_token = '6660841e-54ad-4025-83d1-7226529fa670'
    print(f"测试Token验证: {test_token}")
    
    # 测试同步版本
    print("\n=== 同步版本测试 ===")
    result = verify_mcp_token(token_uuid=test_token)
    if result:
        print("✓ 同步Token验证成功")
    else:
        print("✗ 同步Token验证失败")
    
    # 测试获取token信息（同步）
    token_info = get_mcp_tokens(token_uuid=test_token)
    if token_info:
        print(f"同步Token信息: {token_info}")
    else:
        print("同步版本未找到Token信息")
    
    # 测试异步版本
    print("\n=== 异步版本测试 ===")
    async_result = await verify_mcp_token_async(token_uuid=test_token)
    if async_result:
        print("✓ 异步Token验证成功")
    else:
        print("✗ 异步Token验证失败")
    
    # 测试获取token信息（异步）
    async_token_info = await get_mcp_tokens_async(token_uuid=test_token)
    if async_token_info:
        print(f"异步Token信息: {async_token_info}")
    else:
        print("异步版本未找到Token信息")

if __name__ == "__main__":
    # 运行异步测试
    asyncio.run(main())

