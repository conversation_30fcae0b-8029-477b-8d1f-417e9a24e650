"""
PostgreSQL数据库连接模块
用于连接和操作PostgreSQL数据库
"""

import os
import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from contextlib import contextmanager
from typing import Dict, List, Union, Any, Optional
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置日志
logger = logging.getLogger(__name__)

# PostgreSQL连接配置
PG_HOST = os.getenv('PG_HOST', 'localhost')
PG_PORT = int(os.getenv('PG_PORT', 5432))
PG_USER = os.getenv('PG_USER', 'postgres')
PG_PASSWORD = os.getenv('PG_PASSWORD', '')
PG_DB_CMSDATA = os.getenv('PG_DB_CMSDATA', 'cmsdata')


class PostgreSQLError(Exception):
    """PostgreSQL数据库错误"""
    pass


def connect_postgres(
    host: str = None, 
    port: int = None, 
    database: str = None, 
    user: str = None, 
    password: str = None
) -> psycopg2.extensions.connection:
    """连接PostgreSQL数据库"""
    
    # 使用传入参数或默认配置
    host = host or PG_HOST
    port = port or PG_PORT
    database = database or PG_DB_CMSDATA
    user = user or PG_USER
    password = password or PG_PASSWORD
    
    try:
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=user,
            password=password,
            cursor_factory=RealDictCursor
        )
        logger.debug(f"PostgreSQL连接成功: {host}:{port}/{database}")
        return conn
    except Exception as e:
        raise PostgreSQLError(f"PostgreSQL连接失败: {str(e)}")


@contextmanager
def get_postgres_connection(database: str = None):
    """PostgreSQL连接上下文管理器"""
    conn = None
    try:
        conn = connect_postgres(database=database)
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        raise PostgreSQLError(f"PostgreSQL操作失败: {str(e)}")
    finally:
        if conn:
            conn.close()


def execute_postgres_query(
    query: str,
    params: tuple = None,
    fetch_all: bool = False,
    auto_commit: bool = True,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行PostgreSQL查询"""
    
    with get_postgres_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    if fetch_all:
                        result = cursor.fetchall()
                        return [dict(row) for row in result] if result else []
                    else:
                        result = cursor.fetchone()
                        return dict(result) if result else None
                else:
                    result = cursor.rowcount
                    if auto_commit:
                        conn.commit()
                    return result
                    
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise PostgreSQLError(f"PostgreSQL查询执行错误: {str(e)}")


def execute_postgres_script(
    script: str,
    database: str = None,
    auto_commit: bool = True
) -> bool:
    """执行PostgreSQL脚本（多条SQL语句）"""
    
    with get_postgres_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                cursor.execute(script)
                if auto_commit:
                    conn.commit()
                logger.info("PostgreSQL脚本执行成功")
                return True
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise PostgreSQLError(f"PostgreSQL脚本执行错误: {str(e)}")


def test_postgres_connection(database: str = None) -> bool:
    """测试PostgreSQL连接"""
    try:
        with get_postgres_connection(database=database) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT version();")
                version = cursor.fetchone()
                logger.info(f"PostgreSQL连接测试成功: {version['version']}")
                return True
    except Exception as e:
        logger.error(f"PostgreSQL连接测试失败: {str(e)}")
        return False


def get_table_exists(table_name: str, schema: str = 'public', database: str = None) -> bool:
    """检查表是否存在"""
    query = """
        SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = %s 
            AND table_name = %s
        );
    """
    
    try:
        result = execute_postgres_query(
            query, 
            params=(schema, table_name), 
            database=database
        )
        return result['exists'] if result else False
    except Exception as e:
        logger.error(f"检查表存在性失败: {str(e)}")
        return False


def drop_table_if_exists(table_name: str, schema: str = 'public', database: str = None) -> bool:
    """删除表（如果存在）"""
    query = f"DROP TABLE IF EXISTS {schema}.{table_name} CASCADE;"
    
    try:
        execute_postgres_query(query, database=database)
        logger.info(f"表 {schema}.{table_name} 删除成功")
        return True
    except Exception as e:
        logger.error(f"删除表失败: {str(e)}")
        return False


# 导出常用函数
__all__ = [
    'PostgreSQLError',
    'connect_postgres',
    'get_postgres_connection',
    'execute_postgres_query',
    'execute_postgres_script',
    'test_postgres_connection',
    'get_table_exists',
    'drop_table_if_exists',
    'PG_HOST',
    'PG_PORT',
    'PG_USER',
    'PG_DB_CMSDATA'
]
