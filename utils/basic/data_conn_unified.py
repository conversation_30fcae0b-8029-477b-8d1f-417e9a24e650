# 统一数据库连接模块 - 支持SSH隧道和直连模式

import os
import logging
import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor
import firebirdsql as fdb
from typing import List, Dict, Tuple, Union, Optional
from dotenv import load_dotenv
import threading
from queue import Queue, Empty
from contextlib import contextmanager
from config import Config
from sshtunnel import SSHTunnelForwarder
import time
import socket
import functools
from random import uniform

# 加载环境变量
load_dotenv(override=True)

# 设置日志级别 - 生产环境使用WARNING
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 重试装饰器
def retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    连接重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避系数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (BrokenPipeError, ConnectionError, 
                        psycopg2.OperationalError, fdb.DatabaseError, fdb.OperationalError,
                        socket.error, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        # 对于recv相关错误，增加等待时间
                        if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                            wait_time = delay * (backoff ** attempt) + uniform(1.0, 2.0)  # 额外等待时间
                            logger.warning(f"[RETRY] {func.__name__} 网络接收错误 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        else:
                            wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                            logger.warning(f"[RETRY] {func.__name__} 连接失败 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"[RETRY] {func.__name__} 重试{max_attempts}次后仍然失败: {e}")
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error(f"[RETRY] {func.__name__} 非连接错误: {e}")
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

# 获取统一的环境配置
SSH_HOST = os.getenv("SSH_HOST")
SSH_USER = os.getenv("SSH_USER")
SSH_PASSWORD = os.getenv("SSH_PASSWORD")
APP_ENV = os.getenv("APP_ENV", "development")

# 数据库主机配置
FIREBIRD_HOST = os.getenv("FIREBIRD_HOST")  # JP服务器
PG_HOST = os.getenv("PG_HOST")              # PostgreSQL服务器

# Firebird数据库配置
FIREBIRD_USER = os.getenv("FIREBIRD_USER", "SYSDBA")
FIREBIRD_PASSWORD = os.getenv("FIREBIRD_PASSWORD", "masterkey")
FIREBIRD_DB_PATH = os.getenv("FIREBIRD_DB_PATH")

# PostgreSQL数据库配置  
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_DB_CMSDATA = os.getenv("PG_DB_CMSDATA", "cmsdata")

# 端口配置
FIREBIRD_PORT = int(os.getenv("FIREBIRD_PORT", 33050))
PG_PORT = int(os.getenv("PG_PORT", 5432))

# 环境模式检测
if APP_ENV == "development":
    # 开发环境：Firebird使用SSH隧道，PostgreSQL直连
    USE_SSH_TUNNEL = True
    USE_FALLBACK = True
    USE_LOCAL_TESTING = False
elif APP_ENV == "local_testing":
    # 本地测试环境：使用模拟数据
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = True
else:
    # 生产环境和其他环境：都直连
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = False

class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass

class SSHTunnelManager:
    """SSH隧道管理器"""
    
    def __init__(self):
        self.tunnel = None
        self.local_firebird_port = None
        self.is_connected = False
        self.use_fallback = False
        self.lock = threading.Lock()
    
    def create_tunnel(self):
        """创建SSH隧道（仅用于Firebird）"""
        if not USE_SSH_TUNNEL:
            logger.debug("[SSH_TUNNEL] SSH tunnel not required in current environment")
            return True
            
        if not all([SSH_HOST, SSH_USER, SSH_PASSWORD, FIREBIRD_HOST]):
            if USE_FALLBACK:
                logger.warning("[SSH_TUNNEL] SSH parameters incomplete, will use fallback connection")
                self.use_fallback = True
                return True
            else:
                raise DatabaseError("SSH连接参数不完整，请检查环境变量设置")
        
        with self.lock:
            if self.is_connected and self.tunnel and self.tunnel.is_alive:
                logger.debug("[SSH_TUNNEL] Tunnel already active")
                return True
            
            try:
                logger.debug(f"[SSH_TUNNEL] Creating SSH tunnel to {SSH_HOST}")
                
                # 创建SSH隧道，仅转发Firebird端口
                self.tunnel = SSHTunnelForwarder(
                    (SSH_HOST, 22),
                    ssh_username=SSH_USER,
                    ssh_password=SSH_PASSWORD,
                    remote_bind_addresses=[
                        (FIREBIRD_HOST, FIREBIRD_PORT),  # Firebird端口
                    ],
                    local_bind_addresses=[
                        ('127.0.0.1', 0),  # 让系统自动分配本地端口
                    ],
                    ssh_config_file=None,
                    allow_agent=False,
                    host_pkey_directories=[],
                    compression=True
                )
                
                # 启动隧道
                self.tunnel.start()
                
                # 获取分配的本地端口
                self.local_firebird_port = self.tunnel.local_bind_ports[0]
                
                # 等待隧道建立
                time.sleep(2)
                
                # 验证隧道是否可用
                if self._test_tunnel_connectivity():
                    self.is_connected = True
                    logger.info(f"[SSH_TUNNEL] SSH tunnel created successfully")
                    logger.info(f"[SSH_TUNNEL] Local Firebird port: {self.local_firebird_port}")
                    logger.info(f"[SSH_TUNNEL] PostgreSQL connects directly to {PG_HOST}")
                    return True
                else:
                    self.close_tunnel()
                    raise DatabaseError("SSH隧道连接测试失败")
                    
            except Exception as e:
                logger.error(f"[SSH_TUNNEL] Failed to create SSH tunnel: {e}")
                self.close_tunnel()
                if USE_FALLBACK:
                    logger.warning(f"[SSH_TUNNEL] SSH tunnel failed, switching to fallback connection")
                    self.use_fallback = True
                    return True
                else:
                    raise DatabaseError(f"SSH隧道创建失败: {str(e)}")
    
    def _test_tunnel_connectivity(self):
        """测试隧道连接性"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', self.local_firebird_port))
            sock.close()
            
            if result == 0:
                logger.debug("[SSH_TUNNEL] Firebird port test successful")
                return True
            else:
                logger.warning(f"[SSH_TUNNEL] Firebird port test failed: {result}")
                return False
                
        except Exception as e:
            logger.warning(f"[SSH_TUNNEL] Tunnel connectivity test failed: {e}")
            return False
    
    def close_tunnel(self):
        """关闭SSH隧道"""
        with self.lock:
            if self.tunnel:
                try:
                    self.tunnel.stop()
                    logger.debug("[SSH_TUNNEL] SSH tunnel closed")
                except:
                    pass
                finally:
                    self.tunnel = None
                    self.is_connected = False
                    self.local_firebird_port = None
    
    def get_connection_params(self):
        """获取连接参数"""
        try:
            # PostgreSQL始终直连到PG_HOST
            pg_host = PG_HOST
            pg_port = PG_PORT
            
            # Firebird连接策略
            if USE_SSH_TUNNEL and not self.use_fallback:
                if not self.is_connected:
                    self.create_tunnel()
                
                if self.use_fallback:
                    # SSH隧道失败，Firebird回退到PG_HOST
                    logger.info("[SSH_TUNNEL] Firebird using fallback connection to PG_HOST")
                    firebird_host = PG_HOST
                    firebird_port = FIREBIRD_PORT
                else:
                    # SSH隧道成功，Firebird连接到JP
                    firebird_host = '127.0.0.1'
                    firebird_port = self.local_firebird_port
            else:
                # 直连模式或使用回退连接
                if USE_SSH_TUNNEL and self.use_fallback:
                    firebird_host = PG_HOST
                    logger.info("[SSH_TUNNEL] Firebird using fallback connection to PG_HOST")
                else:
                    firebird_host = FIREBIRD_HOST or PG_HOST
                
                firebird_port = FIREBIRD_PORT
            
            # 确保返回的是完整的字典
            result = {
                'firebird_host': firebird_host,
                'firebird_port': firebird_port,
                'pg_host': pg_host,
                'pg_port': pg_port
            }
            
            # 验证返回值的完整性
            for key in ['firebird_host', 'firebird_port', 'pg_host', 'pg_port']:
                if key not in result or result[key] is None:
                    logger.error(f"[SSH_MANAGER] get_connection_params() 返回的字典缺少 '{key}' 键")
                    raise DatabaseError(f"连接参数配置错误：缺少 '{key}' 参数")
            
            logger.debug(f"[SSH_MANAGER] get_connection_params() 返回: {result}")
            return result
            
        except Exception as e:
            logger.error(f"[SSH_MANAGER] get_connection_params() 发生异常: {e}")
            # 返回一个安全的默认配置
            fallback_params = {
                'firebird_host': PG_HOST or 'localhost',
                'firebird_port': FIREBIRD_PORT,
                'pg_host': PG_HOST or 'localhost',
                'pg_port': PG_PORT
            }
            logger.warning(f"[SSH_MANAGER] 使用回退连接参数: {fallback_params}")
            return fallback_params

# 全局SSH隧道管理器
_ssh_manager = None
_ssh_manager_lock = threading.Lock()

def get_ssh_manager():
    """获取SSH隧道管理器实例"""
    global _ssh_manager
    if _ssh_manager is None:
        with _ssh_manager_lock:
            if _ssh_manager is None:
                try:
                    _ssh_manager = SSHTunnelManager()
                    logger.debug("[GET_SSH_MANAGER] 成功创建 SSHTunnelManager 实例")
                except Exception as e:
                    logger.error(f"[GET_SSH_MANAGER] 创建 SSHTunnelManager 实例失败: {e}")
                    raise DatabaseError(f"无法创建SSH隧道管理器: {str(e)}")
    
    # 确保返回的是正确的实例类型
    if not isinstance(_ssh_manager, SSHTunnelManager):
        logger.error(f"[GET_SSH_MANAGER] 全局 _ssh_manager 变量类型错误: {type(_ssh_manager)}")
        with _ssh_manager_lock:
            _ssh_manager = None
            _ssh_manager = SSHTunnelManager()
            logger.warning("[GET_SSH_MANAGER] 重新创建了 SSHTunnelManager 实例")
    
    return _ssh_manager

class FirebirdConnectionPool:
    """优化的Firebird连接池"""
    
    def __init__(self, max_connections=25, min_connections=5, connection_timeout=60):
        logger.debug("[FB_POOL_INIT] Initializing FirebirdConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        self.connection_timeout = connection_timeout
        self.pool = Queue()
        self.active_connections = 0
        self.lock = threading.RLock()
        
        # 获取SSH管理器和连接参数
        self.ssh_manager = get_ssh_manager()
        conn_params = self.ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[FB_POOL_INIT] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        self.host = conn_params['firebird_host']
        self.port = conn_params['firebird_port']
        self.successful_charset = None
        logger.debug(f"[FB_POOL_INIT] Host: {self.host}, Port: {self.port}")
        
        # 初始化连接池
        logger.debug("[FB_POOL_INIT] Calling _initialize_pool().")
        self._initialize_pool()
        logger.debug("[FB_POOL_INIT] FirebirdConnectionPool initialization complete.")
    
    def _initialize_pool(self):
        """初始化连接池"""
        logger.debug("[FB_POOL_INIT_POOL] Entered _initialize_pool().")
        with self.lock:
            logger.debug(f"[FB_POOL_INIT_POOL] Lock acquired. Initializing {self.min_connections} connections.")
            for i in range(self.min_connections):
                logger.debug(f"[FB_POOL_INIT_POOL] Creating connection {i+1} of {self.min_connections}.")
                try:
                    conn = self._create_connection()
                    if conn:
                        self.pool.put(conn)
                        logger.debug(f"[FB_POOL_INIT_POOL] Connection {i+1} created and added to pool.")
                    else:
                        logger.warning(f"[FB_POOL_INIT_POOL] _create_connection() returned None for connection {i+1}.")
                except Exception as e:
                    logger.warning(f"[FB_POOL_INIT_POOL] Initialization failed for connection {i+1}: {e}")
    
    @retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
    def _create_connection(self):
        """创建新连接"""
        # 尝试不同的字符集，优先使用成功过的字符集
        charsets = ['UTF-8', 'GBK', 'WIN1252', 'ISO8859_1']
        if hasattr(self, 'successful_charset') and self.successful_charset:
            # 将成功的字符集放到首位
            charsets = [self.successful_charset] + [c for c in charsets if c != self.successful_charset]
        
        for charset_to_try in charsets:
            try:
                # 获取最新的连接参数
                ssh_manager = get_ssh_manager()
                conn_params = ssh_manager.get_connection_params()
                
                # 验证 conn_params 是字典类型
                if not isinstance(conn_params, dict):
                    logger.error(f"[FB_POOL_CREATE_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}")
                    raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
                
                # 使用新的配置
                db_path = FIREBIRD_DB_PATH
                if not db_path:
                    raise DatabaseError("未配置FIREBIRD_DB_PATH环境变量")
                
                user = FIREBIRD_USER
                password = FIREBIRD_PASSWORD
                
                # 创建连接
                conn = fdb.connect(
                    host=self.host,
                    port=self.port,
                    database=db_path,
                    user=user,
                    password=password,
                    charset=charset_to_try
                )
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                self.successful_charset = charset_to_try
                with self.lock:
                    self.active_connections += 1
                return PooledConnection(self, conn)
                
            except DatabaseError:
                # 重新抛出数据库错误（如路径未找到）
                raise
            except Exception as e:
                error_msg = str(e).lower()
                logger.debug(f"[FB_POOL_CREATE_CONN] Connection attempt with charset {charset_to_try} failed: {e}")
                
                # 如果是SSH隧道连接失败，且有回退选项，则尝试回退
                if USE_SSH_TUNNEL and USE_FALLBACK and not ssh_manager.use_fallback and "unavailable database" in error_msg:
                    logger.warning(f"[FB_POOL_CREATE_CONN] SSH tunnel database connection failed, attempting fallback")
                    ssh_manager.use_fallback = True
                    # 重新获取连接参数
                    conn_params = ssh_manager.get_connection_params()
                    self.host = conn_params['firebird_host']
                    self.port = conn_params['firebird_port']
                    # 重新尝试连接
                    return self._create_connection()
                continue
        
        logger.error("[FB_POOL_CREATE_CONN] Failed to create connection with any charset.")
        
        # 简化错误提示
        logger.warning("[FB_POOL_CREATE_CONN] 所有字符集连接失败，请检查服务器配置")
        
        # 如果SSH隧道连接失败且有回退选项，尝试回退（但避免无限递归）
        ssh_manager = get_ssh_manager()
        if USE_SSH_TUNNEL and USE_FALLBACK and not ssh_manager.use_fallback:
            logger.warning("[FB_POOL_CREATE_CONN] All charset attempts failed, trying fallback connection")
            ssh_manager.use_fallback = True
            conn_params = ssh_manager.get_connection_params()
            self.host = conn_params['firebird_host']
            self.port = conn_params['firebird_port']
            
            # 直接在这里尝试一次fallback连接，避免递归
            try:
                logger.debug(f"[FB_POOL_CREATE_CONN] Trying fallback: {self.host}:{self.port}")
                conn = fdb.connect(
                    host=self.host,
                    port=self.port,
                    database=FIREBIRD_DB_PATH,
                    user=FIREBIRD_USER,
                    password=FIREBIRD_PASSWORD,
                    charset='UTF-8'  # 回退时只尝试UTF-8
                )
                
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                self.successful_charset = 'UTF-8'
                with self.lock:
                    self.active_connections += 1
                logger.warning(f"[FB_POOL_CREATE_CONN] Fallback connection successful with UTF-8")
                return PooledConnection(self, conn)
                
            except Exception as fallback_e:
                logger.error(f"[FB_POOL_CREATE_CONN] Fallback connection also failed: {fallback_e}")
        
        raise DatabaseError("无法使用任何字符集创建连接")
    
    def get_connection(self):
        """获取连接"""
        try:
            # 尝试从池中获取
            conn = self.pool.get_nowait()
            
            # 验证连接是否有效
            if self._is_connection_valid(conn):
                return conn
            else:
                # 连接无效，创建新连接
                with self.lock:
                    self.active_connections -= 1
                return self._create_connection()
                
        except Empty:
            # 池中没有可用连接
            with self.lock:
                if self.active_connections < self.max_connections:
                    return self._create_connection()
            
            # 等待连接
            try:
                return self.pool.get(timeout=self.connection_timeout)
            except Empty:
                raise DatabaseError(f"无法在 {self.connection_timeout} 秒内获取连接")
    
    def return_connection(self, conn):
        """归还连接"""
        if isinstance(conn, PooledConnection):
            if self._is_connection_valid(conn._conn):
                self.pool.put(conn)
            else:
                with self.lock:
                    self.active_connections -= 1
    
    def _is_connection_valid(self, conn):
        """检查连接是否有效"""
        try:
            if isinstance(conn, PooledConnection):
                conn = conn._conn
            
            # 添加超时控制的连接检查
            with conn.cursor() as cursor:
                cursor.execute("SELECT FIRST 1 1 FROM RDB$DATABASE")
                result = cursor.fetchone()
                if result is None:
                    logger.warning("[FB_POOL_VALID] Connection test returned None")
                    return False
            return True
        except Exception as e:
            # 降低recv错误的日志级别，因为这是正常的连接池清理行为
            if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                logger.debug(f"[FB_POOL_VALID] Connection expired (normal cleanup): {e}")
            else:
                logger.warning(f"[FB_POOL_VALID] Connection validation failed: {e}")
            return False
    
    def cleanup_invalid_connections(self):
        """清理无效连接"""
        logger.debug("[FB_POOL_CLEANUP] Starting connection cleanup")
        invalid_connections = []
        valid_connections = []
        
        # 检查池中的所有连接
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if self._is_connection_valid(conn):
                    valid_connections.append(conn)
                else:
                    invalid_connections.append(conn)
            except Empty:
                break
        
        # 关闭无效连接
        for conn in invalid_connections:
            try:
                if isinstance(conn, PooledConnection):
                    conn._conn.close()
                else:
                    conn.close()
                with self.lock:
                    self.active_connections -= 1
            except Exception as e:
                logger.warning(f"[FB_POOL_CLEANUP] Error closing invalid connection: {e}")
        
        # 将有效连接放回池中
        for conn in valid_connections:
            self.pool.put(conn)
        
        logger.info(f"[FB_POOL_CLEANUP] Cleaned up {len(invalid_connections)} invalid connections, {len(valid_connections)} valid")
    
    def close_all(self):
        """关闭所有连接"""
        logger.debug("[FB_POOL_CLOSE_ALL] Closing all connections")
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if isinstance(conn, PooledConnection):
                    conn._conn.close()
                else:
                    conn.close()
            except Exception as e:
                logger.warning(f"[FB_POOL_CLOSE_ALL] Error closing connection: {e}")
        
        with self.lock:
            self.active_connections = 0
        logger.debug("[FB_POOL_CLOSE_ALL] All connections closed")

class PooledConnection:
    """池化连接包装器"""
    
    def __init__(self, pool, conn):
        self.pool = pool
        self._conn = conn
    
    def __enter__(self):
        return self._conn
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.pool.return_connection(self)
    
    def cursor(self):
        return self._conn.cursor()
    
    def commit(self):
        return self._conn.commit()
    
    def rollback(self):
        return self._conn.rollback()
    
    def close(self):
        """关闭连接时自动归还到池"""
        self.pool.return_connection(self)
    
    def __getattr__(self, name):
        """代理所有其他属性到实际连接"""
        return getattr(self._conn, name)

# 全局连接池实例
_firebird_pool = None
_pool_lock = threading.Lock()

def get_firebird_pool():
    """获取Firebird连接池实例"""
    global _firebird_pool
    logger.debug("[GET_FIREBIRD_POOL] Entered function.")
    if _firebird_pool is None:
        logger.debug("[GET_FIREBIRD_POOL] _firebird_pool is None. Attempting to acquire lock.")
        with _pool_lock:
            logger.debug("[GET_FIREBIRD_POOL] Lock acquired.")
            if _firebird_pool is None:
                logger.debug("[GET_FIREBIRD_POOL] _firebird_pool is still None. Initializing.")
                try:
                    logger.debug("[GET_FIREBIRD_POOL] Calling Config.get_config_dict().")
                    config = Config.get_config_dict()
                    logger.debug(f"[GET_FIREBIRD_POOL] Config.get_config_dict() returned: {config}")
                    logger.debug("[GET_FIREBIRD_POOL] Initializing FirebirdConnectionPool.")
                    _firebird_pool = FirebirdConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2),
                        connection_timeout=config.get('fb_pool_connection_request_timeout', 10)
                    )
                    logger.debug("[GET_FIREBIRD_POOL] FirebirdConnectionPool initialized.")
                except Exception as e:
                    logger.error(f"[GET_FIREBIRD_POOL] Exception during pool initialization: {e}", exc_info=True)
                    raise
            else:
                logger.debug("[GET_FIREBIRD_POOL] _firebird_pool was initialized by another thread.")
        logger.debug("[GET_FIREBIRD_POOL] Lock released.")
    else:
        logger.debug("[GET_FIREBIRD_POOL] _firebird_pool already initialized.")
    logger.debug("[GET_FIREBIRD_POOL] Returning _firebird_pool.")
    return _firebird_pool

@contextmanager
def get_pooled_pro2_connection(charset_preference: str = 'UTF-8'):
    """获取池化的Firebird连接（上下文管理器）"""
    logger.debug(f"[GET_POOLED_PRO2_CONN] Attempting to get connection. Charset preference: {charset_preference}")
    pool = get_firebird_pool()
    conn = None
    try:
        logger.debug(f"[GET_POOLED_PRO2_CONN] Calling pool.get_connection(). Pool active: {pool.active_connections}, Max: {pool.max_connections}, Pool size: {pool.pool.qsize()}")
        conn = pool.get_connection()
        
        # 如果需要特定字符集且池中连接的字符集不匹配，创建新的专用连接
        if charset_preference != 'UTF-8' and charset_preference != pool.successful_charset:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Pool connection charset ({pool.successful_charset}) doesn't match preference ({charset_preference}). Creating dedicated connection.")
            # 归还池连接
            if conn:
                conn.close()
            
            # 创建专用连接
            conn = create_dedicated_connection(charset_preference)
        
        if conn:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Successfully got connection from pool. Yielding connection.")
        else:
            logger.error(f"[GET_POOLED_PRO2_CONN] pool.get_connection() returned None.")
        yield conn
    except Exception as e:
        logger.error(f"[GET_POOLED_PRO2_CONN] Exception during pool.get_connection() or yield: {e}", exc_info=True)
        raise
    finally:
        if conn:
            logger.debug(f"[GET_POOLED_PRO2_CONN] Closing connection in finally block.")
            if hasattr(conn, '_is_dedicated') and conn._is_dedicated:
                # 专用连接直接关闭
                conn._conn.close()
            else:
                # 池连接归还到池
                conn.close()
        else:
            logger.debug(f"[GET_POOLED_PRO2_CONN] No connection to close in finally block.")

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def create_dedicated_connection(charset: str):
    """创建指定字符集的专用连接"""
    logger.debug(f"[CREATE_DEDICATED_CONN] Creating dedicated connection with charset: {charset}")
    
    try:
        # 获取SSH管理器和连接参数
        ssh_manager = get_ssh_manager()
        conn_params = ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[CREATE_DEDICATED_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        host = conn_params['firebird_host']
        port = conn_params['firebird_port']
        
        # 使用新的配置
        db_path = FIREBIRD_DB_PATH
        if not db_path:
            raise DatabaseError("未配置FIREBIRD_DB_PATH环境变量")
        
        user = FIREBIRD_USER
        password = FIREBIRD_PASSWORD
        
        logger.debug(f"[CREATE_DEDICATED_CONN] Connecting with charset: {charset}")
        
        # 创建连接（移除不支持的参数）
        conn = fdb.connect(
            host=host,
            port=port,
            database=db_path,
            user=user,
            password=password,
            charset=charset
        )
        
        # 测试连接
        with conn.cursor() as cursor:
            cursor.execute("SELECT FIRST 1 1 FROM RDB$DATABASE")
            cursor.fetchone()
        
        logger.debug(f"[CREATE_DEDICATED_CONN] Dedicated connection created successfully with charset: {charset}")
        
        # 创建包装器，标记为专用连接
        wrapper = DedicatedConnection(conn)
        return wrapper
        
    except Exception as e:
        logger.error(f"[CREATE_DEDICATED_CONN] Failed to create dedicated connection with charset {charset}: {e}")
        
        # 如果是非UTF-8字符集失败，尝试UTF-8回退
        if charset != 'UTF-8':
            logger.warning(f"[CREATE_DEDICATED_CONN] Retrying with UTF-8 charset as fallback")
            try:
                conn = fdb.connect(
                    host=host,
                    port=port,
                    database=db_path,
                    user=user,
                    password=password,
                    charset='UTF-8'
                )
                
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                logger.warning(f"[CREATE_DEDICATED_CONN] UTF-8 fallback successful")
                wrapper = DedicatedConnection(conn)
                return wrapper
                
            except Exception as fallback_e:
                logger.error(f"[CREATE_DEDICATED_CONN] UTF-8 fallback also failed: {fallback_e}")
        
        raise DatabaseError(f"无法创建专用连接，字符集: {charset}, 错误: {str(e)}")

class DedicatedConnection:
    """专用连接包装器"""
    
    def __init__(self, conn):
        self._conn = conn
        self._is_dedicated = True
    
    def cursor(self):
        return self._conn.cursor()
    
    def commit(self):
        return self._conn.commit()
    
    def rollback(self):
        return self._conn.rollback()
    
    def close(self):
        """专用连接直接关闭，不归还到池"""
        if self._conn:
            self._conn.close()
    
    def __getattr__(self, name):
        """代理所有其他属性到实际连接"""
        return getattr(self._conn, name)

# PostgreSQL连接池类
class PostgreSQLConnectionPool:
    """优化的PostgreSQL连接池"""
    
    def __init__(self, max_connections=25, min_connections=5):
        logger.debug("[PG_POOL_INIT] Initializing PostgreSQLConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        
        # 获取SSH管理器和连接参数
        self.ssh_manager = get_ssh_manager()
        conn_params = self.ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[PG_POOL_INIT] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        self.host = conn_params['pg_host']
        self.port = conn_params['pg_port']
        
        # 创建连接池
        try:
            self.pool = psycopg2.pool.SimpleConnectionPool(
                minconn=min_connections,
                maxconn=max_connections,
                host=self.host,
                port=self.port,
                database=PG_DB_CMSDATA,
                user=PG_USER,
                password=PG_PASSWORD,
                cursor_factory=RealDictCursor,
                connect_timeout=10
            )
            logger.info(f"[PG_POOL_INIT] PostgreSQL连接池初始化成功: {self.host}:{self.port}/{PG_DB_CMSDATA}")
        except Exception as e:
            logger.error(f"[PG_POOL_INIT] PostgreSQL连接池初始化失败: {e}")
            raise DatabaseError(f"PostgreSQL连接池初始化失败: {str(e)}")
    
    def get_connection(self):
        """从连接池获取连接"""
        try:
            conn = self.pool.getconn()
            if conn:
                logger.debug("[PG_POOL] 从连接池获取连接成功")
                return conn
            else:
                raise DatabaseError("无法从连接池获取连接")
        except Exception as e:
            logger.error(f"[PG_POOL] 获取连接失败: {e}")
            raise DatabaseError(f"PostgreSQL连接池获取连接失败: {str(e)}")
    
    def put_connection(self, conn):
        """将连接返回给连接池"""
        try:
            self.pool.putconn(conn)
            logger.debug("[PG_POOL] 连接已返回连接池")
        except Exception as e:
            logger.error(f"[PG_POOL] 返回连接失败: {e}")
    
    def close_all(self):
        """关闭所有连接"""
        try:
            self.pool.closeall()
            logger.info("[PG_POOL] PostgreSQL连接池已关闭")
        except Exception as e:
            logger.error(f"[PG_POOL] 关闭连接池失败: {e}")

# 全局PostgreSQL连接池实例
_pg_pool = None
_pg_pool_lock = threading.Lock()

def get_postgres_pool():
    """获取PostgreSQL连接池实例"""
    global _pg_pool
    if _pg_pool is None:
        with _pg_pool_lock:
            if _pg_pool is None:
                try:
                    config = Config.get_config_dict()
                    _pg_pool = PostgreSQLConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2)
                    )
                except Exception as e:
                    logger.error(f"[GET_PG_POOL] 初始化PostgreSQL连接池失败: {e}")
                    raise
    return _pg_pool

@contextmanager
def get_postgres_connection(database: str = None):
    """获取PostgreSQL连接（上下文管理器）"""
    pool = get_postgres_pool()
    conn = None
    try:
        conn = pool.get_connection()
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"[GET_PG_CONN] PostgreSQL连接操作失败: {e}")
        raise DatabaseError(f"PostgreSQL连接操作失败: {str(e)}")
    finally:
        if conn:
            pool.put_connection(conn)

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def connect_postgres(host: str = None, port: int = None, database: str = None) -> psycopg2.extensions.connection:
    """直接连接PostgreSQL数据库（不使用连接池）"""
    # 获取SSH管理器和连接参数
    ssh_manager = get_ssh_manager()
    conn_params = ssh_manager.get_connection_params()
    
    # 验证 conn_params 是字典类型
    if not isinstance(conn_params, dict):
        logger.error(f"[PG_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
        raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
    
    if host is None:
        host = conn_params['pg_host']
    if port is None:
        port = conn_params['pg_port']
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    
    logger.debug(f"[PG_CONN] Connecting to PostgreSQL: {host}:{port}/{database}")
    
    try:
        return psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=PG_USER,
            password=PG_PASSWORD,
            cursor_factory=RealDictCursor,
            connect_timeout=10
        )
    except Exception as e:
        raise DatabaseError(f"PostgreSQL连接失败: {str(e)}")

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def execute_postgres_query(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False,
    auto_commit: bool = True,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行PostgreSQL查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock PostgreSQL query execution: {query[:100]}...")
        return _get_mock_postgres_data(query, fetch_all)
    
    with get_postgres_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    if fetch_all:
                        result = cursor.fetchall()
                        return [dict(row) for row in result] if result else []
                    else:
                        result = cursor.fetchone()
                        return dict(result) if result else None
                else:
                    result = cursor.rowcount
                    if auto_commit:
                        conn.commit()
                    return result
                
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise DatabaseError(f"PostgreSQL查询执行错误: {str(e)}")

def _get_mock_postgres_data(query: str, fetch_all: bool = False):
    """返回模拟的PostgreSQL数据"""
    query_upper = query.upper()
    
    if "SELECT" in query_upper:
        if fetch_all:
            return [
                {"id": 1, "name": "测试数据1", "value": 100},
                {"id": 2, "name": "测试数据2", "value": 200}
            ]
        else:
            return {"id": 1, "name": "测试数据", "value": 100}
    else:
        # INSERT, UPDATE, DELETE 操作返回影响的行数
        return 1

def execute_pro2_query(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False, 
    charset: str = 'UTF-8'
) -> Union[Tuple, List[Tuple], None]:
    """执行Firebird查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock Firebird query execution: {query[:100]}...")
        return _get_mock_pro2_data(query, fetch_all)
    
    with get_pooled_pro2_connection(charset_preference=charset) as conn:
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            if query.strip().upper().startswith(("INSERT", "UPDATE", "DELETE")):
                conn.commit()
                return cursor.rowcount
            elif fetch_all:
                return cursor.fetchall()
            else:
                return cursor.fetchone()

def _get_mock_pro2_data(query: str, fetch_all: bool = False):
    """返回模拟的Firebird数据"""
    query_upper = query.upper()
    
    # 根据查询类型返回不同的模拟数据
    if "USERS" in query_upper:
        if fetch_all:
            return [
                (1, "testuser", "Test User", "测试部门"),
                (2, "admin", "Admin User", "管理部门")
            ]
        else:
            return (1, "testuser", "Test User", "测试部门")
    
    elif "JOB" in query_upper or "BOOKING" in query_upper:
        if fetch_all:
            return [
                ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员"),
                ("JOB002", 2, "测试客户2", "2025-05-02", 2000.0, "测试操作员2")
            ]
        else:
            return ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员")
    
    elif "PROFIT" in query_upper:
        if fetch_all:
            return [
                (1000.0, 800.0, 200.0),
                (2000.0, 1500.0, 500.0)
            ]
        else:
            return (1000.0, 800.0, 200.0)
    
    else:
        # 默认返回空结果
        if fetch_all:
            return []
        else:
            return None

# 异步支持函数（向后兼容）
import asyncio
import asyncpg
from contextlib import asynccontextmanager

async def execute_pro2_query_async(query: str, params: tuple = None, fetch_all: bool = False, charset: str = 'UTF-8'):
    """异步执行Firebird查询"""
    return await asyncio.to_thread(execute_pro2_query, query, params, fetch_all, charset)

async def execute_postgres_query_async(query: str, params: tuple = None, fetch_all: bool = False, 
                                      auto_commit: bool = True, database: str = None):
    """异步执行PostgreSQL查询"""
    return await asyncio.to_thread(execute_postgres_query, query, params, fetch_all, auto_commit, database)

@asynccontextmanager
async def get_postgres_connection_async(database: str = None):
    """异步PostgreSQL连接上下文管理器"""
    # 获取SSH管理器和连接参数
    ssh_manager = get_ssh_manager()
    conn_params = ssh_manager.get_connection_params()
    
    # 验证 conn_params 是字典类型
    if not isinstance(conn_params, dict):
        logger.error(f"[ASYNC_PG_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
        raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
    
    host = conn_params['pg_host']
    port = conn_params['pg_port']
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    
    logger.debug(f"[ASYNC_PG_CONN] Connecting to PostgreSQL: {host}:{port}/{database}")
    
    conn = None
    try:
        # 创建异步PostgreSQL连接
        logger.debug(f"[ASYNC_PG_CONN] 尝试连接参数: host={host}, port={port}, user={PG_USER}, database={database}")
        conn = await asyncpg.connect(
            host=host,
            port=port,
            user=PG_USER,
            password=PG_PASSWORD,
            database=database,
            timeout=10
        )
        yield conn
    except Exception as e:
        logger.error(f"[ASYNC_PG_CONN] PostgreSQL连接失败: {e}")
        logger.error(f"[ASYNC_PG_CONN] 异常类型: {type(e)}")
        import traceback
        logger.error(f"[ASYNC_PG_CONN] 详细堆栈: {traceback.format_exc()}")
        raise DatabaseError(f"异步PostgreSQL连接失败: {str(e)}")
    finally:
        if conn:
            await conn.close()

# 清理函数
def cleanup_invalid_connections_periodically():
    """定期清理无效连接（保持连接池活跃）"""
    global _firebird_pool
    
    if _firebird_pool:
        try:
            _firebird_pool.cleanup_invalid_connections()
            logger.debug("[PERIODIC_CLEANUP] Firebird connection pool cleaned")
        except Exception as e:
            logger.error(f"[PERIODIC_CLEANUP] Error during periodic cleanup: {e}")

def cleanup_connections():
    """清理所有连接"""
    global _firebird_pool, _pg_pool, _ssh_manager
    
    logger.info("[CLEANUP] Starting connection cleanup")
    
    # 清理Firebird连接池
    if _firebird_pool:
        try:
            _firebird_pool.close_all()
            logger.info("[CLEANUP] Firebird connection pool closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing Firebird pool: {e}")
        finally:
            _firebird_pool = None
    
    # 清理PostgreSQL连接池
    if _pg_pool:
        try:
            _pg_pool.close_all()
            logger.info("[CLEANUP] PostgreSQL connection pool closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing PostgreSQL pool: {e}")
        finally:
            _pg_pool = None
    
    # 清理SSH隧道
    if _ssh_manager:
        try:
            _ssh_manager.close_tunnel()
            logger.info("[CLEANUP] SSH tunnel closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing SSH tunnel: {e}")
        finally:
            _ssh_manager = None
    
    logger.info("[CLEANUP] Connection cleanup completed")

# 安全格式化日期
def safe_format_date(date_value, format_str='%Y-%m-%d'):
    """安全格式化日期，处理各种日期类型"""
    import pandas as pd
    from datetime import datetime, date
    
    if date_value is None or pd.isna(date_value):
        return None
    
    # 如果已经是字符串，直接返回（假设已经是正确格式）
    if isinstance(date_value, str):
        # 尝试解析字符串日期并重新格式化
        try:
            # 常见的日期格式
            date_formats = [
                '%Y-%m-%d',
                '%Y/%m/%d', 
                '%d/%m/%Y',
                '%m/%d/%Y',
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S'
            ]
            
            for fmt in date_formats:
                try:
                    parsed_date = datetime.strptime(date_value.strip(), fmt)
                    return parsed_date.strftime(format_str)
                except ValueError:
                    continue
            
            # 如果无法解析，返回原字符串
            return date_value
        except:
            return date_value
    
    # 如果是datetime或date对象
    if isinstance(date_value, (datetime, date)):
        try:
            return date_value.strftime(format_str)
        except:
            return str(date_value)
    
    # 如果是pandas的Timestamp
    if hasattr(date_value, 'strftime'):
        try:
            return date_value.strftime(format_str)
        except:
            return str(date_value)
    
    # 其他情况，尝试转换为字符串
    return str(date_value) if date_value else None

import atexit
atexit.register(cleanup_connections)

# 导出常用函数
__all__ = [
    'DatabaseError',
    'SSHTunnelManager',
    'get_ssh_manager', 
    'get_pooled_pro2_connection',
    'connect_postgres',
    'get_postgres_connection',
    'get_postgres_connection_async',
    'execute_postgres_query',
    'execute_postgres_query_async',
    'execute_pro2_query',
    'execute_pro2_query_async',
    'cleanup_connections',
    'PG_DB_CMSDATA'
]