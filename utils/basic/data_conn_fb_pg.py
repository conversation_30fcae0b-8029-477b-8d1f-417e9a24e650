# -*- coding: utf-8 -*-
# Firebird & PostgreSQL 数据库连接模块
# 统一管理 Firebird 和 PostgreSQL 数据库连接，支持连接池

import os
import logging
import threading
import time
import socket
import functools
from queue import Queue, Empty
from contextlib import contextmanager
from typing import List, Dict, Tuple, Union, Optional
from random import uniform

# Firebird imports
import firebirdsql as fdb

# PostgreSQL imports (同步)
import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor

# PostgreSQL imports (异步)
import asyncpg
import asyncio
from contextlib import asynccontextmanager

# SSH tunnel support
from sshtunnel import SSHTunnelForwarder
from dotenv import load_dotenv
from config import Config

# 加载环境变量
load_dotenv(override=True)

# 设置日志级别
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 重试装饰器
def retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    连接重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避系数
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except (BrokenPipeError, ConnectionError, 
                        psycopg2.OperationalError, fdb.DatabaseError, fdb.OperationalError,
                        socket.error, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        # 对于recv相关错误，增加等待时间
                        if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                            wait_time = delay * (backoff ** attempt) + uniform(1.0, 2.0)
                            logger.warning(f"[RETRY] {func.__name__} 网络接收错误 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        else:
                            wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                            logger.warning(f"[RETRY] {func.__name__} 连接失败 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        time.sleep(wait_time)
                    else:
                        logger.error(f"[RETRY] {func.__name__} 重试{max_attempts}次后仍然失败: {e}")
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error(f"[RETRY] {func.__name__} 非连接错误: {e}")
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

# 环境配置
SSH_HOST = os.getenv("SSH_HOST")
SSH_USER = os.getenv("SSH_USER")
SSH_PASSWORD = os.getenv("SSH_PASSWORD")
APP_ENV = os.getenv("APP_ENV", "development")

# Firebird数据库配置
FIREBIRD_HOST = os.getenv("FIREBIRD_HOST")
FIREBIRD_PORT = int(os.getenv("FIREBIRD_PORT", 33050))
FIREBIRD_USER = os.getenv("FIREBIRD_USER", "SYSDBA")
FIREBIRD_PASSWORD = os.getenv("FIREBIRD_PASSWORD", "masterkey")
FIREBIRD_DB_PATH = os.getenv("FIREBIRD_DB_PATH")

# PostgreSQL数据库配置
PG_HOST = os.getenv("PG_HOST")
PG_PORT = int(os.getenv("PG_PORT", 5432))
PG_USER = os.getenv("PG_USER")
PG_PASSWORD = os.getenv("PG_PASSWORD")
PG_DB_CMSDATA = os.getenv("PG_DB_CMSDATA", "cmsdata")

# 环境模式检测
if APP_ENV == "development":
    # 开发环境：Firebird使用SSH隧道，PostgreSQL直连
    USE_SSH_TUNNEL = True
    USE_FALLBACK = True
    USE_LOCAL_TESTING = False
elif APP_ENV == "local_testing":
    # 本地测试环境：使用模拟数据
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = True
else:
    # 生产环境和其他环境：都直连
    USE_SSH_TUNNEL = False
    USE_FALLBACK = False
    USE_LOCAL_TESTING = False

class DatabaseError(Exception):
    """自定义数据库异常类"""
    pass

class SSHTunnelManager:
    """SSH隧道管理器（仅用于Firebird）"""
    
    def __init__(self):
        self.tunnel = None
        self.local_firebird_port = None
        self.is_connected = False
        self.use_fallback = False
        self.lock = threading.Lock()
    
    def create_tunnel(self):
        """创建SSH隧道（仅用于Firebird）"""
        if not USE_SSH_TUNNEL:
            logger.debug("[SSH_TUNNEL] SSH tunnel not required in current environment")
            return True
            
        if not all([SSH_HOST, SSH_USER, SSH_PASSWORD, FIREBIRD_HOST]):
            if USE_FALLBACK:
                logger.warning("[SSH_TUNNEL] SSH parameters incomplete, will use fallback connection")
                self.use_fallback = True
                return True
            else:
                raise DatabaseError("SSH连接参数不完整，请检查环境变量设置")
        
        with self.lock:
            if self.is_connected and self.tunnel and self.tunnel.is_alive:
                logger.debug("[SSH_TUNNEL] Tunnel already active")
                return True
            
            try:
                logger.debug(f"[SSH_TUNNEL] Creating SSH tunnel to {SSH_HOST}")
                
                # 创建SSH隧道，仅转发Firebird端口
                self.tunnel = SSHTunnelForwarder(
                    (SSH_HOST, 22),
                    ssh_username=SSH_USER,
                    ssh_password=SSH_PASSWORD,
                    remote_bind_addresses=[
                        (FIREBIRD_HOST, FIREBIRD_PORT),  # Firebird端口
                    ],
                    local_bind_addresses=[
                        ('127.0.0.1', 0),  # 让系统自动分配本地端口
                    ],
                    ssh_config_file=None,
                    allow_agent=False,
                    host_pkey_directories=[],
                    compression=True
                )
                
                # 启动隧道
                self.tunnel.start()
                
                # 获取分配的本地端口
                self.local_firebird_port = self.tunnel.local_bind_ports[0]
                
                # 等待隧道建立
                time.sleep(2)
                
                # 验证隧道是否可用
                if self._test_tunnel_connectivity():
                    self.is_connected = True
                    logger.info(f"[SSH_TUNNEL] SSH tunnel created successfully")
                    logger.info(f"[SSH_TUNNEL] Local Firebird port: {self.local_firebird_port}")
                    logger.info(f"[SSH_TUNNEL] PostgreSQL connects directly to {PG_HOST}")
                    return True
                else:
                    self.close_tunnel()
                    raise DatabaseError("SSH隧道连接测试失败")
                    
            except Exception as e:
                logger.error(f"[SSH_TUNNEL] Failed to create SSH tunnel: {e}")
                self.close_tunnel()
                if USE_FALLBACK:
                    logger.warning(f"[SSH_TUNNEL] SSH tunnel failed, switching to fallback connection")
                    self.use_fallback = True
                    return True
                else:
                    raise DatabaseError(f"SSH隧道创建失败: {str(e)}")
    
    def _test_tunnel_connectivity(self):
        """测试隧道连接性"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('127.0.0.1', self.local_firebird_port))
            sock.close()
            
            if result == 0:
                logger.debug("[SSH_TUNNEL] Firebird port test successful")
                return True
            else:
                logger.warning(f"[SSH_TUNNEL] Firebird port test failed: {result}")
                return False
                
        except Exception as e:
            logger.warning(f"[SSH_TUNNEL] Tunnel connectivity test failed: {e}")
            return False
    
    def close_tunnel(self):
        """关闭SSH隧道"""
        with self.lock:
            if self.tunnel:
                try:
                    self.tunnel.stop()
                    logger.debug("[SSH_TUNNEL] SSH tunnel closed")
                except:
                    pass
                finally:
                    self.tunnel = None
                    self.is_connected = False
                    self.local_firebird_port = None
    
    def get_connection_params(self):
        """获取连接参数"""
        try:
            # PostgreSQL始终直连到PG_HOST
            pg_host = PG_HOST
            pg_port = PG_PORT
            
            # Firebird连接策略
            if USE_SSH_TUNNEL and not self.use_fallback:
                if not self.is_connected:
                    self.create_tunnel()
                
                if self.use_fallback:
                    # SSH隧道失败，Firebird回退到PG_HOST
                    logger.info("[SSH_TUNNEL] Firebird using fallback connection to PG_HOST")
                    firebird_host = PG_HOST
                    firebird_port = FIREBIRD_PORT
                else:
                    # SSH隧道成功，Firebird连接到JP
                    firebird_host = '127.0.0.1'
                    firebird_port = self.local_firebird_port
            else:
                # 直连模式或使用回退连接
                if USE_SSH_TUNNEL and self.use_fallback:
                    firebird_host = PG_HOST
                    logger.info("[SSH_TUNNEL] Firebird using fallback connection to PG_HOST")
                else:
                    firebird_host = FIREBIRD_HOST or PG_HOST
                
                firebird_port = FIREBIRD_PORT
            
            # 确保返回的是完整的字典
            result = {
                'firebird_host': firebird_host,
                'firebird_port': firebird_port,
                'pg_host': pg_host,
                'pg_port': pg_port
            }
            
            # 验证返回值的完整性
            for key in ['firebird_host', 'firebird_port', 'pg_host', 'pg_port']:
                if key not in result or result[key] is None:
                    logger.error(f"[SSH_MANAGER] get_connection_params() 返回的字典缺少 '{key}' 键")
                    raise DatabaseError(f"连接参数配置错误：缺少 '{key}' 参数")
            
            logger.debug(f"[SSH_MANAGER] get_connection_params() 返回: {result}")
            return result
            
        except Exception as e:
            logger.error(f"[SSH_MANAGER] get_connection_params() 发生异常: {e}")
            # 返回一个安全的默认配置
            fallback_params = {
                'firebird_host': PG_HOST or 'localhost',
                'firebird_port': FIREBIRD_PORT,
                'pg_host': PG_HOST or 'localhost',
                'pg_port': PG_PORT
            }
            logger.warning(f"[SSH_MANAGER] 使用回退连接参数: {fallback_params}")
            return fallback_params

# 全局SSH隧道管理器
_ssh_manager = None
_ssh_manager_lock = threading.Lock()

def get_ssh_manager():
    """获取SSH隧道管理器实例"""
    global _ssh_manager
    if _ssh_manager is None:
        with _ssh_manager_lock:
            if _ssh_manager is None:
                try:
                    _ssh_manager = SSHTunnelManager()
                    logger.debug("[GET_SSH_MANAGER] 成功创建 SSHTunnelManager 实例")
                except Exception as e:
                    logger.error(f"[GET_SSH_MANAGER] 创建 SSHTunnelManager 实例失败: {e}")
                    raise DatabaseError(f"无法创建SSH隧道管理器: {str(e)}")
    
    return _ssh_manager

# ====================
# Firebird 连接池实现
# ====================

class FirebirdConnectionPool:
    """优化的Firebird连接池"""
    
    def __init__(self, max_connections=25, min_connections=5, connection_timeout=60):
        logger.debug("[FB_POOL_INIT] Initializing FirebirdConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        self.connection_timeout = connection_timeout
        self.pool = Queue()
        self.active_connections = 0
        self.lock = threading.RLock()
        
        # 获取SSH管理器和连接参数
        self.ssh_manager = get_ssh_manager()
        conn_params = self.ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[FB_POOL_INIT] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        self.host = conn_params['firebird_host']
        self.port = conn_params['firebird_port']
        self.successful_charset = None
        logger.debug(f"[FB_POOL_INIT] Host: {self.host}, Port: {self.port}")
        
        # 初始化连接池
        self._initialize_pool()
        logger.debug("[FB_POOL_INIT] FirebirdConnectionPool initialization complete.")
    
    def _initialize_pool(self):
        """初始化连接池"""
        logger.debug("[FB_POOL_INIT_POOL] Entered _initialize_pool().")
        with self.lock:
            logger.debug(f"[FB_POOL_INIT_POOL] Initializing {self.min_connections} connections.")
            for i in range(self.min_connections):
                try:
                    conn = self._create_connection()
                    if conn:
                        self.pool.put(conn)
                        logger.debug(f"[FB_POOL_INIT_POOL] Connection {i+1} created and added to pool.")
                    else:
                        logger.warning(f"[FB_POOL_INIT_POOL] _create_connection() returned None for connection {i+1}.")
                except Exception as e:
                    logger.warning(f"[FB_POOL_INIT_POOL] Initialization failed for connection {i+1}: {e}")
    
    @retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
    def _create_connection(self):
        """创建新连接"""
        # 尝试不同的字符集，优先使用成功过的字符集
        charsets = ['UTF-8', 'GBK', 'WIN1252', 'ISO8859_1']
        if hasattr(self, 'successful_charset') and self.successful_charset:
            # 将成功的字符集放到首位
            charsets = [self.successful_charset] + [c for c in charsets if c != self.successful_charset]
        
        for charset_to_try in charsets:
            try:
                # 获取最新的连接参数
                ssh_manager = get_ssh_manager()
                conn_params = ssh_manager.get_connection_params()
                
                # 验证 conn_params 是字典类型
                if not isinstance(conn_params, dict):
                    logger.error(f"[FB_POOL_CREATE_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}")
                    raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
                
                # 使用新的配置
                db_path = FIREBIRD_DB_PATH
                if not db_path:
                    raise DatabaseError("未配置FIREBIRD_DB_PATH环境变量")
                
                user = FIREBIRD_USER
                password = FIREBIRD_PASSWORD
                
                # 创建连接
                conn = fdb.connect(
                    host=self.host,
                    port=self.port,
                    database=db_path,
                    user=user,
                    password=password,
                    charset=charset_to_try
                )
                # 测试连接
                with conn.cursor() as cursor:
                    cursor.execute("SELECT 1 FROM RDB$DATABASE")
                    cursor.fetchone()
                
                self.successful_charset = charset_to_try
                with self.lock:
                    self.active_connections += 1
                return PooledConnection(self, conn)
                
            except DatabaseError:
                # 重新抛出数据库错误（如路径未找到）
                raise
            except Exception as e:
                logger.debug(f"[FB_POOL_CREATE_CONN] Connection attempt with charset {charset_to_try} failed: {e}")
                continue
        
        logger.error("[FB_POOL_CREATE_CONN] Failed to create connection with any charset.")
        raise DatabaseError("无法使用任何字符集创建连接")
    
    def get_connection(self):
        """获取连接"""
        try:
            # 尝试从池中获取
            conn = self.pool.get_nowait()
            
            # 验证连接是否有效
            if self._is_connection_valid(conn):
                return conn
            else:
                # 连接无效，创建新连接
                with self.lock:
                    self.active_connections -= 1
                return self._create_connection()
                
        except Empty:
            # 池中没有可用连接
            with self.lock:
                if self.active_connections < self.max_connections:
                    return self._create_connection()
            
            # 等待连接
            try:
                return self.pool.get(timeout=self.connection_timeout)
            except Empty:
                raise DatabaseError(f"无法在 {self.connection_timeout} 秒内获取连接")
    
    def return_connection(self, conn):
        """归还连接"""
        if isinstance(conn, PooledConnection):
            if self._is_connection_valid(conn._conn):
                self.pool.put(conn)
            else:
                with self.lock:
                    self.active_connections -= 1
    
    def _is_connection_valid(self, conn):
        """检查连接是否有效"""
        try:
            if isinstance(conn, PooledConnection):
                conn = conn._conn
            
            # 添加超时控制的连接检查
            with conn.cursor() as cursor:
                cursor.execute("SELECT FIRST 1 1 FROM RDB$DATABASE")
                result = cursor.fetchone()
                if result is None:
                    logger.warning("[FB_POOL_VALID] Connection test returned None")
                    return False
            return True
        except Exception as e:
            # 降低recv错误的日志级别，因为这是正常的连接池清理行为
            if "recv" in str(e).lower() or "can not recv" in str(e).lower():
                logger.debug(f"[FB_POOL_VALID] Connection expired (normal cleanup): {e}")
            else:
                logger.warning(f"[FB_POOL_VALID] Connection validation failed: {e}")
            return False
    
    def close_all(self):
        """关闭所有连接"""
        logger.debug("[FB_POOL_CLOSE_ALL] Closing all connections")
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                if isinstance(conn, PooledConnection):
                    conn._conn.close()
                else:
                    conn.close()
            except Exception as e:
                logger.warning(f"[FB_POOL_CLOSE_ALL] Error closing connection: {e}")
        
        with self.lock:
            self.active_connections = 0
        logger.debug("[FB_POOL_CLOSE_ALL] All connections closed")

class PooledConnection:
    """池化连接包装器"""
    
    def __init__(self, pool, conn):
        self.pool = pool
        self._conn = conn
    
    def __enter__(self):
        return self._conn
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.pool.return_connection(self)
    
    def cursor(self):
        return self._conn.cursor()
    
    def commit(self):
        return self._conn.commit()
    
    def rollback(self):
        return self._conn.rollback()
    
    def close(self):
        """关闭连接时自动归还到池"""
        self.pool.return_connection(self)
    
    def __getattr__(self, name):
        """代理所有其他属性到实际连接"""
        return getattr(self._conn, name)

# 全局Firebird连接池实例
_firebird_pool = None
_firebird_pool_lock = threading.Lock()

def get_firebird_pool():
    """获取Firebird连接池实例"""
    global _firebird_pool
    if _firebird_pool is None:
        with _firebird_pool_lock:
            if _firebird_pool is None:
                try:
                    config = Config.get_config_dict()
                    _firebird_pool = FirebirdConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2),
                        connection_timeout=config.get('fb_pool_connection_request_timeout', 10)
                    )
                except Exception as e:
                    logger.error(f"[GET_FIREBIRD_POOL] Exception during pool initialization: {e}")
                    raise
    return _firebird_pool

@contextmanager
def get_pooled_pro2_connection(charset_preference: str = 'UTF-8'):
    """获取池化的Firebird连接（上下文管理器）"""
    logger.debug(f"[GET_POOLED_PRO2_CONN] Attempting to get connection. Charset preference: {charset_preference}")
    pool = get_firebird_pool()
    conn = None
    try:
        conn = pool.get_connection()
        logger.debug(f"[GET_POOLED_PRO2_CONN] Successfully got connection from pool.")
        yield conn
    except Exception as e:
        logger.error(f"[GET_POOLED_PRO2_CONN] Exception during pool.get_connection() or yield: {e}")
        raise
    finally:
        if conn:
            conn.close()

def execute_pro2_query(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False, 
    charset: str = 'UTF-8'
) -> Union[Tuple, List[Tuple], None]:
    """执行Firebird查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock Firebird query execution: {query[:100]}...")
        return _get_mock_pro2_data(query, fetch_all)
    
    with get_pooled_pro2_connection(charset_preference=charset) as conn:
        with conn.cursor() as cursor:
            cursor.execute(query, params)
            
            if query.strip().upper().startswith(("INSERT", "UPDATE", "DELETE")):
                conn.commit()
                return cursor.rowcount
            elif fetch_all:
                return cursor.fetchall()
            else:
                return cursor.fetchone()

# ====================
# PostgreSQL 连接池实现
# ====================

class PostgreSQLConnectionPool:
    """优化的PostgreSQL连接池"""
    
    def __init__(self, max_connections=25, min_connections=5):
        logger.debug("[PG_POOL_INIT] Initializing PostgreSQLConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        
        # 获取SSH管理器和连接参数
        self.ssh_manager = get_ssh_manager()
        conn_params = self.ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[PG_POOL_INIT] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        self.host = conn_params['pg_host']
        self.port = conn_params['pg_port']
        
        # 创建连接池
        try:
            self.pool = psycopg2.pool.SimpleConnectionPool(
                minconn=min_connections,
                maxconn=max_connections,
                host=self.host,
                port=self.port,
                database=PG_DB_CMSDATA,
                user=PG_USER,
                password=PG_PASSWORD,
                cursor_factory=RealDictCursor,
                connect_timeout=10
            )
            logger.info(f"[PG_POOL_INIT] PostgreSQL连接池初始化成功: {self.host}:{self.port}/{PG_DB_CMSDATA}")
        except Exception as e:
            logger.error(f"[PG_POOL_INIT] PostgreSQL连接池初始化失败: {e}")
            raise DatabaseError(f"PostgreSQL连接池初始化失败: {str(e)}")
    
    def get_connection(self):
        """从连接池获取连接"""
        try:
            conn = self.pool.getconn()
            if conn:
                logger.debug("[PG_POOL] 从连接池获取连接成功")
                return conn
            else:
                raise DatabaseError("无法从连接池获取连接")
        except Exception as e:
            logger.error(f"[PG_POOL] 获取连接失败: {e}")
            raise DatabaseError(f"PostgreSQL连接池获取连接失败: {str(e)}")
    
    def put_connection(self, conn):
        """将连接返回给连接池"""
        try:
            self.pool.putconn(conn)
            logger.debug("[PG_POOL] 连接已返回连接池")
        except Exception as e:
            logger.error(f"[PG_POOL] 返回连接失败: {e}")
    
    def close_all(self):
        """关闭所有连接"""
        try:
            self.pool.closeall()
            logger.info("[PG_POOL] PostgreSQL连接池已关闭")
        except Exception as e:
            logger.error(f"[PG_POOL] 关闭连接池失败: {e}")

# 全局PostgreSQL连接池实例
_pg_pool = None
_pg_pool_lock = threading.Lock()

def get_postgres_pool():
    """获取PostgreSQL连接池实例"""
    global _pg_pool
    if _pg_pool is None:
        with _pg_pool_lock:
            if _pg_pool is None:
                try:
                    config = Config.get_config_dict()
                    _pg_pool = PostgreSQLConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2)
                    )
                except Exception as e:
                    logger.error(f"[GET_PG_POOL] 初始化PostgreSQL连接池失败: {e}")
                    raise
    return _pg_pool

@contextmanager
def get_postgres_connection(database: str = None):
    """获取PostgreSQL连接（上下文管理器）"""
    pool = get_postgres_pool()
    conn = None
    try:
        conn = pool.get_connection()
        yield conn
    except Exception as e:
        if conn:
            conn.rollback()
        logger.error(f"[GET_PG_CONN] PostgreSQL连接操作失败: {e}")
        raise DatabaseError(f"PostgreSQL连接操作失败: {str(e)}")
    finally:
        if conn:
            pool.put_connection(conn)

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def connect_postgres(host: str = None, port: int = None, database: str = None) -> psycopg2.extensions.connection:
    """直接连接PostgreSQL数据库（不使用连接池）"""
    # 获取SSH管理器和连接参数
    ssh_manager = get_ssh_manager()
    conn_params = ssh_manager.get_connection_params()
    
    # 验证 conn_params 是字典类型
    if not isinstance(conn_params, dict):
        logger.error(f"[PG_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
        raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
    
    if host is None:
        host = conn_params['pg_host']
    if port is None:
        port = conn_params['pg_port']
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    
    logger.debug(f"[PG_CONN] Connecting to PostgreSQL: {host}:{port}/{database}")
    
    try:
        return psycopg2.connect(
            host=host,
            port=port,
            database=database,
            user=PG_USER,
            password=PG_PASSWORD,
            cursor_factory=RealDictCursor,
            connect_timeout=10
        )
    except Exception as e:
        raise DatabaseError(f"PostgreSQL连接失败: {str(e)}")

@retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
def execute_postgres_query(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False,
    auto_commit: bool = True,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行PostgreSQL查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock PostgreSQL query execution: {query[:100]}...")
        return _get_mock_postgres_data(query, fetch_all)
    
    with get_postgres_connection(database=database) as conn:
        with conn.cursor() as cursor:
            try:
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                if query.strip().upper().startswith('SELECT'):
                    if fetch_all:
                        result = cursor.fetchall()
                        return [dict(row) for row in result] if result else []
                    else:
                        result = cursor.fetchone()
                        return dict(result) if result else None
                else:
                    result = cursor.rowcount
                    if auto_commit:
                        conn.commit()
                    return result
                
            except Exception as e:
                if auto_commit:
                    conn.rollback()
                raise DatabaseError(f"PostgreSQL查询执行错误: {str(e)}")

# ====================
# PostgreSQL 异步连接实现
# ====================

class AsyncPostgreSQLConnectionPool:
    """异步PostgreSQL连接池"""
    
    def __init__(self, max_connections=25, min_connections=5):
        logger.debug("[ASYNC_PG_POOL_INIT] Initializing AsyncPostgreSQLConnectionPool...")
        self.max_connections = max_connections
        self.min_connections = min_connections
        self._pool = None
        self._lock = asyncio.Lock()
        
        # 获取SSH管理器和连接参数
        self.ssh_manager = get_ssh_manager()
        conn_params = self.ssh_manager.get_connection_params()
        
        # 验证 conn_params 是字典类型
        if not isinstance(conn_params, dict):
            logger.error(f"[ASYNC_PG_POOL_INIT] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
            raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
        
        self.host = conn_params['pg_host']
        self.port = conn_params['pg_port']
        
        logger.info(f"[ASYNC_PG_POOL_INIT] 异步PostgreSQL连接池配置: {self.host}:{self.port}/{PG_DB_CMSDATA}")
    
    async def initialize(self):
        """初始化异步连接池"""
        if self._pool is None:
            async with self._lock:
                if self._pool is None:
                    try:
                        self._pool = await asyncpg.create_pool(
                            host=self.host,
                            port=self.port,
                            database=PG_DB_CMSDATA,
                            user=PG_USER,
                            password=PG_PASSWORD,
                            min_size=self.min_connections,
                            max_size=self.max_connections,
                            timeout=10,
                            command_timeout=60
                        )
                        logger.info(f"[ASYNC_PG_POOL_INIT] 异步PostgreSQL连接池初始化成功: {self.host}:{self.port}/{PG_DB_CMSDATA}")
                    except Exception as e:
                        logger.error(f"[ASYNC_PG_POOL_INIT] 异步PostgreSQL连接池初始化失败: {e}")
                        raise DatabaseError(f"异步PostgreSQL连接池初始化失败: {str(e)}")
        return self._pool
    
    async def get_connection(self):
        """获取异步连接"""
        pool = await self.initialize()
        return await pool.acquire()
    
    async def put_connection(self, conn):
        """释放异步连接"""
        if self._pool:
            await self._pool.release(conn)
    
    async def close_all(self):
        """关闭异步连接池"""
        if self._pool:
            await self._pool.close()
            self._pool = None
            logger.info("[ASYNC_PG_POOL] 异步PostgreSQL连接池已关闭")

# 全局异步PostgreSQL连接池实例
_async_pg_pool = None
_async_pg_pool_lock = asyncio.Lock()

async def get_async_postgres_pool():
    """获取异步PostgreSQL连接池实例"""
    global _async_pg_pool
    if _async_pg_pool is None:
        async with _async_pg_pool_lock:
            if _async_pg_pool is None:
                try:
                    config = Config.get_config_dict()
                    _async_pg_pool = AsyncPostgreSQLConnectionPool(
                        max_connections=config.get('fb_pool_max_connections', 15),
                        min_connections=config.get('fb_pool_min_connections', 2)
                    )
                except Exception as e:
                    logger.error(f"[GET_ASYNC_PG_POOL] 初始化异步PostgreSQL连接池失败: {e}")
                    raise
    return _async_pg_pool

@asynccontextmanager
async def get_async_postgres_connection(database: str = None):
    """获取异步PostgreSQL连接（上下文管理器）"""
    pool = await get_async_postgres_pool()
    conn = None
    try:
        conn = await pool.get_connection()
        yield conn
    except Exception as e:
        logger.error(f"[GET_ASYNC_PG_CONN] 异步PostgreSQL连接操作失败: {e}")
        raise DatabaseError(f"异步PostgreSQL连接操作失败: {str(e)}")
    finally:
        if conn:
            await pool.put_connection(conn)

async def execute_postgres_query_async(
    query: str, 
    params: tuple = None, 
    fetch_all: bool = False,
    database: str = None
) -> Union[Dict, List[Dict], int, None]:
    """执行异步PostgreSQL查询"""
    # 本地测试模式：返回模拟数据
    if USE_LOCAL_TESTING:
        logger.info(f"[LOCAL_TESTING] Mock async PostgreSQL query execution: {query[:100]}...")
        return _get_mock_postgres_data(query, fetch_all)
    
    async with get_async_postgres_connection(database=database) as conn:
        try:
            if query.strip().upper().startswith('SELECT'):
                if params:
                    if fetch_all:
                        result = await conn.fetch(query, *params)
                        return [dict(row) for row in result] if result else []
                    else:
                        result = await conn.fetchrow(query, *params)
                        return dict(result) if result else None
                else:
                    if fetch_all:
                        result = await conn.fetch(query)
                        return [dict(row) for row in result] if result else []
                    else:
                        result = await conn.fetchrow(query)
                        return dict(result) if result else None
            else:
                # 对于INSERT, UPDATE, DELETE操作
                if params:
                    result = await conn.execute(query, *params)
                else:
                    result = await conn.execute(query)
                
                # 解析结果获取影响的行数
                if result.startswith('INSERT'):
                    return int(result.split()[-1])  # INSERT 0 1 -> 1
                elif result.startswith('UPDATE'):
                    return int(result.split()[1])   # UPDATE 1 -> 1
                elif result.startswith('DELETE'):
                    return int(result.split()[1])   # DELETE 1 -> 1
                else:
                    return 0
        except Exception as e:
            raise DatabaseError(f"异步PostgreSQL查询执行错误: {str(e)}")

# 异步重试装饰器
def async_retry_on_connection_error(max_attempts=3, delay=1.0, backoff=2.0):
    """
    异步连接重试装饰器
    """
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except (ConnectionError, asyncpg.ConnectionDoesNotExistError, 
                        asyncpg.InterfaceError, OSError) as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        wait_time = delay * (backoff ** attempt) + uniform(0, 0.1)
                        logger.warning(f"[ASYNC_RETRY] {func.__name__} 连接失败 (尝试 {attempt + 1}/{max_attempts}): {e}, {wait_time:.2f}秒后重试")
                        await asyncio.sleep(wait_time)
                    else:
                        logger.error(f"[ASYNC_RETRY] {func.__name__} 重试{max_attempts}次后仍然失败: {e}")
                        break
                except Exception as e:
                    # 非连接相关错误直接抛出
                    logger.error(f"[ASYNC_RETRY] {func.__name__} 非连接错误: {e}")
                    raise
            
            # 所有重试都失败了，抛出最后一个异常
            raise last_exception
        return wrapper
    return decorator

@async_retry_on_connection_error(max_attempts=3, delay=0.5, backoff=2.0)
async def connect_postgres_async(host: str = None, port: int = None, database: str = None) -> asyncpg.Connection:
    """直接创建异步PostgreSQL连接（不使用连接池）"""
    # 获取SSH管理器和连接参数
    ssh_manager = get_ssh_manager()
    conn_params = ssh_manager.get_connection_params()
    
    # 验证 conn_params 是字典类型
    if not isinstance(conn_params, dict):
        logger.error(f"[ASYNC_PG_CONN] get_connection_params() 返回了非字典类型: {type(conn_params)}, 值: {conn_params}")
        raise DatabaseError(f"SSH管理器返回了错误的连接参数类型: {type(conn_params)}")
    
    if host is None:
        host = conn_params['pg_host']
    if port is None:
        port = conn_params['pg_port']
    if database is None:
        database = PG_DB_CMSDATA  # 默认数据库
    
    logger.debug(f"[ASYNC_PG_CONN] Connecting to PostgreSQL: {host}:{port}/{database}")
    
    try:
        return await asyncpg.connect(
            host=host,
            port=port,
            database=database,
            user=PG_USER,
            password=PG_PASSWORD,
            timeout=10
        )
    except Exception as e:
        raise DatabaseError(f"异步PostgreSQL连接失败: {str(e)}")

async def test_async_postgres_connection():
    """测试异步PostgreSQL连接"""
    try:
        async with get_async_postgres_connection() as conn:
            result = await conn.fetchrow("SELECT version();")
            logger.info(f"异步PostgreSQL连接测试成功: {result}")
            return True
    except Exception as e:
        logger.error(f"异步PostgreSQL连接测试失败: {str(e)}")
        return False

async def test_async_all_connections():
    """测试所有异步数据库连接"""
    logger.info("开始测试异步数据库连接...")
    
    pg_result = await test_async_postgres_connection()
    
    if pg_result:
        logger.info("所有异步数据库连接测试通过！")
        return True
    else:
        logger.error("异步数据库连接测试失败！")
        return False

# ====================
# 模拟数据函数
# ====================

def _get_mock_pro2_data(query: str, fetch_all: bool = False):
    """返回模拟的Firebird数据"""
    query_upper = query.upper()
    
    # 根据查询类型返回不同的模拟数据
    if "USERS" in query_upper:
        if fetch_all:
            return [
                (1, "testuser", "Test User", "测试部门"),
                (2, "admin", "Admin User", "管理部门")
            ]
        else:
            return (1, "testuser", "Test User", "测试部门")
    
    elif "JOB" in query_upper or "BOOKING" in query_upper:
        if fetch_all:
            return [
                ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员"),
                ("JOB002", 2, "测试客户2", "2025-05-02", 2000.0, "测试操作员2")
            ]
        else:
            return ("JOB001", 1, "测试客户", "2025-05-01", 1000.0, "测试操作员")
    
    elif "PROFIT" in query_upper:
        if fetch_all:
            return [
                (1000.0, 800.0, 200.0),
                (2000.0, 1500.0, 500.0)
            ]
        else:
            return (1000.0, 800.0, 200.0)
    
    else:
        # 默认返回空结果
        if fetch_all:
            return []
        else:
            return None

def _get_mock_postgres_data(query: str, fetch_all: bool = False):
    """返回模拟的PostgreSQL数据"""
    query_upper = query.upper()
    
    if "SELECT" in query_upper:
        if fetch_all:
            return [
                {"id": 1, "name": "测试数据1", "value": 100},
                {"id": 2, "name": "测试数据2", "value": 200}
            ]
        else:
            return {"id": 1, "name": "测试数据", "value": 100}
    else:
        # INSERT, UPDATE, DELETE 操作返回影响的行数
        return 1

# ====================
# 清理函数
# ====================

def cleanup_connections():
    """清理所有连接"""
    global _firebird_pool, _pg_pool, _ssh_manager
    
    logger.info("[CLEANUP] Starting connection cleanup")
    
    # 清理Firebird连接池
    if _firebird_pool:
        try:
            _firebird_pool.close_all()
            logger.info("[CLEANUP] Firebird connection pool closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing Firebird pool: {e}")
        finally:
            _firebird_pool = None
    
    # 清理PostgreSQL连接池
    if _pg_pool:
        try:
            _pg_pool.close_all()
            logger.info("[CLEANUP] PostgreSQL connection pool closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing PostgreSQL pool: {e}")
        finally:
            _pg_pool = None
    
    # 清理SSH隧道
    if _ssh_manager:
        try:
            _ssh_manager.close_tunnel()
            logger.info("[CLEANUP] SSH tunnel closed")
        except Exception as e:
            logger.error(f"[CLEANUP] Error closing SSH tunnel: {e}")
        finally:
            _ssh_manager = None
    
    logger.info("[CLEANUP] Connection cleanup completed")

# 注册清理函数
import atexit
atexit.register(cleanup_connections)

# ====================
# 测试函数
# ====================

def test_firebird_connection():
    """测试Firebird连接"""
    try:
        with get_pooled_pro2_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT COUNT(*) FROM RDB$DATABASE")
                result = cursor.fetchone()
                logger.info(f"Firebird连接测试成功: {result}")
                return True
    except Exception as e:
        logger.error(f"Firebird连接测试失败: {str(e)}")
        return False

def test_postgres_connection():
    """测试PostgreSQL连接"""
    try:
        with get_postgres_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT version();")
                result = cursor.fetchone()
                logger.info(f"PostgreSQL连接测试成功: {result}")
                return True
    except Exception as e:
        logger.error(f"PostgreSQL连接测试失败: {str(e)}")
        return False

def test_all_connections():
    """测试所有数据库连接"""
    logger.info("开始测试数据库连接...")
    
    fb_result = test_firebird_connection()
    pg_result = test_postgres_connection()
    
    if fb_result and pg_result:
        logger.info("所有数据库连接测试通过！")
        return True
    else:
        logger.error("部分数据库连接测试失败！")
        return False

# 导出常用函数
__all__ = [
    'DatabaseError',
    'SSHTunnelManager',
    'get_ssh_manager',
    
    # Firebird 相关
    'FirebirdConnectionPool',
    'get_firebird_pool',
    'get_pooled_pro2_connection',
    'execute_pro2_query',
    
    # PostgreSQL 相关
    'PostgreSQLConnectionPool',
    'get_postgres_pool',
    'connect_postgres',
    'get_postgres_connection',
    'execute_postgres_query',
    
    # 异步PostgreSQL相关
    'AsyncPostgreSQLConnectionPool',
    'get_async_postgres_pool',
    'get_async_postgres_connection',
    'execute_postgres_query_async',
    'connect_postgres_async',
    'test_async_postgres_connection',
    'test_async_all_connections',
    'async_retry_on_connection_error',
    
    # 测试函数
    'test_firebird_connection',
    'test_postgres_connection',
    'test_all_connections',
    
    # 清理函数
    'cleanup_connections',
    
    # 常量
    'PG_DB_CMSDATA'
]

if __name__ == "__main__":
    # 运行测试
    test_all_connections()